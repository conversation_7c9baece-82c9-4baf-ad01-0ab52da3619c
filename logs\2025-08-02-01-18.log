🔄 重置完成，新参数：抽奖总次数7次、单直播间4次、连续检测11次、无理想福袋7个、上划总次数6个、等待15分钟
初始设定累计6个直播间无福袋或无理想福袋后切换到刷视频模式
创建新的福袋分析器
当前有一台设备连接，编号:192.168.5.219:5555.
使用设备 192.168.5.219:5555 开始执行福袋抽奖
检查抖音极速版是否已启动...
检测到抖音极速版进程存在，但不在前台，需要重新启动
准备启动抖音极速版...
正在启动抖音极速版...
检测到屏幕处于熄灭状态
正在尝试唤醒屏幕...
检测到屏幕处于点亮状态
屏幕已成功唤醒
已关闭可能存在的抖音极速版后台进程
等待应用加载 12 秒...
已确认抖音极速版成功启动并处于前台
本次设定福袋倒计时超过15分钟时跳过
检测到抖音极速版正在运行且在前台
本次设定连续检测12次(约360秒)无福袋后切换直播间
本次设定总共参与7次福袋抽奖后切换到刷视频模式
当前直播间最多抽取5次福袋
本次设定累计6个直播间无理想福袋后切换到刷视频模式
本次设定上划6个直播间后切换到刷视频模式
01:19:16 获取屏幕截图
检测当前页面是否为直播间界面...
01:19:18 获取屏幕截图
未检测到评论输入框关键词，不在直播间
当前不在直播间，开始智能导航至直播间...
开始智能导航至直播间...(回退尝试: 0/5, 重启尝试: 否)
01:19:20 获取屏幕截图
检测当前页面是否为直播间界面...
01:19:23 获取屏幕截图
未检测到评论输入框关键词，不在直播间
检测到关键词: 关注
当前界面在首页推荐视频界面
当前在首页/视频页面，导航到个人中心
设备分辨率为：1080*2280
等待页面加载 9 秒...
01:19:38 获取屏幕截图
当前界面在个人中心
成功进入个人中心，继续导航
等待页面加载 7 秒...
01:19:48 获取屏幕截图
检测到关键词: 互关, 关注, 粉丝
当前界面在用户关注列表
成功进入关注列表，准备打开直播列表
等待页面加载 3 秒...
01:19:54 获取屏幕截图
当前界面已经在直播间列表
成功进入直播列表，准备进入直播间
01:19:56 获取屏幕截图
当前界面已经在直播间列表
当前时间01点
下划刷新直播间列表
01:20:05 获取屏幕截图
直播间列表存在直播的内容
点击打开第一个直播间
01:20:14 获取屏幕截图
检测当前页面是否为直播间界面...
01:20:15 获取屏幕截图
未检测到评论输入框关键词，不在直播间
01:20:16 获取屏幕截图
检测当前页面是否为直播间界面...
01:20:18 获取屏幕截图
检测到评论输入框提示文字: '说点什么'，确认在直播间
已经在直播间内
01:20:25 获取屏幕截图
检测当前页面是否为直播间界面...
01:20:27 获取屏幕截图
检测到评论输入框提示文字: '说点什么'，确认在直播间
成功进入直播间
主循环第1次执行
检测当前页面是否为直播间界面...
01:20:30 获取屏幕截图
检测到评论输入框提示文字: '说点什么'，确认在直播间
01:20:33 获取屏幕截图
01:20:36 获取屏幕截图
01:20:38 获取屏幕截图
01:20:41 获取屏幕截图
当前直播间正常进行中
检测到福袋，原始x坐标: 34，将在随机位置点击 (59, 318)
✅ 检测到福袋，重置连续检测计数
检测到福袋，随机等待18秒后参与抽奖...
Traceback (most recent call last):
  File "F:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\douyin_guaji.py", line 210, in <module>
    choujiang.guaji(0)  # 参数值可根据需要调整
  File "F:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\douyin_guaji.py", line 69, in guaji
    self.analyser.fudai_choujiang(device_id, y_pianyi, True, 15)  # True是不断切换直播间false则不切换，15分钟等待时间
  File "F:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\douyin_fudai.py", line 1464, in fudai_choujiang
    self.operation.delay(delay_time)
  File "F:\zuomianwenjian\3.10.11\1213111\douyin_guaji-main\Underlying_Operations.py", line 1005, in delay
    time.sleep(seconds)
KeyboardInterrupt
