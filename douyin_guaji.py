from douyin_fudai import fudai_analyse
import time
import threading
import random
import traceback
import logging

class fudai_guaji:
    def __init__(self, external_analyser=None):
        """
        初始化福袋挂机对象
        
        参数:
        external_analyser: 可选，外部传入的fudai_analyse对象，如果提供则使用该对象
                          否则创建新的fudai_analyse对象
        """
        # 如果外部提供了分析器对象，则使用它，否则创建新的
        if external_analyser is not None and isinstance(external_analyser, fudai_analyse):
            self.analyser = external_analyser
            print("使用外部提供的福袋分析器")
        else:
            self.analyser = fudai_analyse()
            print("创建新的福袋分析器")
            
        self.max_retries = 5  # 增加最大重试次数
        self._stop_event = threading.Event()  # 用于信号线程停止

    def check_and_start_douyin(self, device_id):
        """
        检查抖音极速版是否在运行并处于前台，不在则启动
        """
        print("检查抖音极速版是否已启动...")
        is_running = self.analyser.operation.is_douyin_running(device_id)
        
        if not is_running:
            print("准备启动抖音极速版...")
            start_success = self.analyser.operation.start_douyin_app(device_id)
            if start_success:
                return True
            else:
                print("抖音极速版启动失败，请检查应用是否已安装")
                return False
        else:
            print("抖音极速版已在前台运行，无需启动")
            return True

    def guaji(self, y_pianyi=0):
        """
        y_pianyi 对应y轴高度的偏移值
        程序会自动识别当前界面并导航到直播间
        """
        retries = 0
        while retries < self.max_retries:
            try:
                device_id = self.analyser.operation.select_device()
                if device_id:
                    print(f"使用设备 {device_id} 开始执行福袋抽奖")
                    
                    # 检查抖音极速版是否在运行，不在则启动
                    if not self.check_and_start_douyin(device_id):
                        print("无法启动抖音极速版，等待15秒后重试")
                        retries += 1
                        self.analyser.operation.delay(15)
                        continue
                    
                    # 将停止事件传递给分析器
                    self.analyser.set_stop_event(self._stop_event)

                    self.analyser.fudai_choujiang(device_id, y_pianyi, True, 15)  # True是不断切换直播间false则不切换，15分钟等待时间
                    return True
                else:
                    print(f"未检测到设备，等待15秒后重试 ({retries+1}/{self.max_retries})")
                    retries += 1
                    self.analyser.operation.delay(15)  # 增加等待时间
            except Exception as e:
                # 记录详细错误信息
                error_msg = f"发生错误: {str(e)}\n{traceback.format_exc()}"
                print(error_msg)
                retries += 1
                # 根据重试次数增加等待时间
                wait_time = 10 + retries * 5
                print(f"等待{wait_time}秒后重试 ({retries}/{self.max_retries})")
                self.analyser.operation.delay(wait_time)
        
        print("已达到最大重试次数，请检查设备连接或重启程序")
        return False
        
    def guaji_with_timeout(self, y_pianyi=0, timeout_seconds=None):
        """
        带超时的挂机方法
        
        参数:
        y_pianyi: y轴偏移值
        timeout_seconds: 超时时间（秒），如果为None则不设超时
        
        返回:
        执行线程对象
        """
        # 重置停止标志
        self._stop_event.clear()
        
        # 创建挂机线程
        def run_task():
            start_time = time.time()
            retries = 0
            max_consecutive_errors = 3  # 最大连续错误次数
            consecutive_errors = 0
            
            # 获取设备ID
            device_id = self.analyser.operation.select_device()
            if not device_id:
                print("未检测到设备，挂机任务结束")
                return

            # 检查抖音极速版是否在运行，不在则启动
            if not self.check_and_start_douyin(device_id):
                print("无法启动抖音极速版，挂机任务结束")
                return
                
            print(f"使用设备 {device_id} 开始执行福袋抽奖（最大运行时间: {timeout_seconds//3600}小时{(timeout_seconds%3600)//60}分钟）")
            
            # 运行直到超时或被手动停止
            while not self._stop_event.is_set():
                try:
                    # 检查是否达到超时
                    if timeout_seconds and (time.time() - start_time) > timeout_seconds:
                        print(f"已达到预设运行时间 {timeout_seconds//3600}小时{(timeout_seconds%3600)//60}分钟，准备退出")
                        break
                        
                    # 执行福袋抽奖
                    current_runtime = time.time() - start_time
                    remaining_time = timeout_seconds - current_runtime if timeout_seconds else None
                    if remaining_time and remaining_time <= 0:
                        break
                        
                    # 计算本次执行的等待时间，确保不会超过总时间限制
                    wait_minutes = 15
                    if remaining_time and remaining_time < wait_minutes * 60:
                        wait_minutes = int(remaining_time / 60)
                        
                    # 将停止事件传递给分析器
                    self.analyser.set_stop_event(self._stop_event)

                    # 执行一次抽奖循环
                    self.analyser.fudai_choujiang(device_id, y_pianyi, True, wait_minutes)
                    
                    # 重置连续错误计数
                    consecutive_errors = 0
                    
                    # 随机休息一会，更像真人操作
                    if not self._stop_event.is_set() and (timeout_seconds is None or (time.time() - start_time) < timeout_seconds):
                        wait_time = random.randint(5, 20)
                        print(f"短暂休息 {wait_time} 秒后继续...")
                        # 分段睡眠，便于响应停止信号
                        for _ in range(wait_time):
                            if self._stop_event.is_set():
                                break
                            time.sleep(1)
                            
                except Exception as e:
                    # 记录详细错误信息
                    error_msg = f"执行过程中出错: {str(e)}\n{traceback.format_exc()}"
                    print(error_msg)
                    consecutive_errors += 1
                    retries += 1
                    
                    # 如果连续错误次数过多，则退出
                    if consecutive_errors >= max_consecutive_errors:
                        print(f"连续出现{consecutive_errors}次错误，挂机任务结束")
                        break
                        
                    # 等待后重试，随着错误次数增加等待时间
                    wait_time = 30 + consecutive_errors * 15
                    print(f"等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
            
            print("挂机任务结束")
            
        # 创建并启动线程
        thread = threading.Thread(target=run_task)
        thread.daemon = True  # 设置为守护线程，这样主程序退出时线程会自动结束
        thread.start()
        return thread
        
    def stop_guaji(self):
        """停止当前挂机任务"""
        self._stop_event.set()
        print("已发送停止信号，程序将在当前操作完成后退出")
        
        # 等待所有操作完成，确保干净退出
        max_wait_time = 30  # 最多等待30秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # 检查是否还有正在进行的操作
            if hasattr(self.analyser, 'is_processing') and self.analyser.is_processing:
                print(f"等待正在进行的操作完成，已等待{int(time.time() - start_time)}秒...")
                time.sleep(1)
            else:
                print("所有操作已完成，程序干净退出")
                break
        
        # 如果超时，强制退出
        if time.time() - start_time >= max_wait_time:
            print(f"等待超时({max_wait_time}秒)，强制退出")

if __name__ == '__main__':
    choujiang = fudai_guaji()
    # 设置参数：y轴偏移值，根据手机屏幕大小调整
    choujiang.guaji(0)  # 参数值可根据需要调整
