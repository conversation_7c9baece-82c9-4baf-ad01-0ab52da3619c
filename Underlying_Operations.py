from paddleocr import PaddleOCR
import subprocess
from datetime import datetime, timedelta
import os
import time
from PIL import Image, ImageFilter
import numpy as np
import logging
import re
import random
import math

class ConnectionManager:
    """ADB连接管理类"""
    
    def __init__(self):
        self.connected_devices = {}  # 存储连接的设备 {device_id: {ip, port, last_check_time}}
        self.connection_check_interval = 60  # 每隔60秒检查一次连接
        self.last_check_time = 0

    def get_connected_devices(self):
        """获取所有已连接设备列表"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, check=True)
            pattern = r'(\b(?:[0-9]{1,3}(?:\.[0-9]{1,3}){3}(?::[0-9]+)?|[A-Za-z0-9]{8,})\b)\s*device\b'
            return re.findall(pattern, result.stdout)
        except subprocess.SubprocessError:
            print("获取设备列表失败")
            return []

    def check_device_connection(self, device_id):
        """检查特定设备是否仍然连接"""
        devices = self.get_connected_devices()
        return device_id in devices

    def reconnect_device(self, device_id):
        """尝试重新连接设备"""
        if not device_id in self.connected_devices:
            print(f"设备 {device_id} 未在连接记录中")
            return False
            
        device_info = self.connected_devices[device_id]
        if ":" in device_id:  # 无线连接
            ip_address = device_id.split(":")[0]
            port = device_id.split(":")[1]
            try:
                # 先断开连接
                subprocess.run(['adb', 'disconnect', device_id], check=False)
                time.sleep(2)
                # 重新连接
                subprocess.run(['adb', 'connect', device_id], check=True)
                print(f"已重新连接到设备: {device_id}")
                return self.check_device_connection(device_id)
            except subprocess.SubprocessError:
                print(f"重新连接设备 {device_id} 失败")
                return False
        else:
            print(f"设备 {device_id} 是USB连接，请检查物理连接")
            return False
            
    def register_device(self, device_id, ip=None, port=None):
        """注册设备到管理器"""
        self.connected_devices[device_id] = {
            'ip': ip,
            'port': port,
            'last_check_time': time.time()
        }
        
    def periodic_connection_check(self):
        """定期检查所有设备连接状态"""
        current_time = time.time()
        if current_time - self.last_check_time < self.connection_check_interval:
            return
            
        self.last_check_time = current_time
        connected_devices = self.get_connected_devices()
        
        for device_id in list(self.connected_devices.keys()):
            if device_id not in connected_devices:
                print(f"设备 {device_id} 连接已断开，尝试重新连接")
                self.reconnect_device(device_id)

class underlying_operations:
    """底层操作类"""
    def __init__(self):
        self.last_cmd_time = 0  # 最小命令间隔（秒）
        self.min_cmd_interval = 0.2  # 最小命令间隔（秒）
        # 超时设置
        self.adb_command_timeout = 15  # ADB命令超时时间(秒)
        self.screenshot_timeout = 30    # 截图超时时间(秒) 
        self.ocr_timeout = 20           # 文字识别超时时间(秒)
        self.tap_delay = (0.5, 1.5)     # 点击后延迟时间范围(秒)
        
        # 截图清理设置
        self.max_screenshots = 100      # 保留的最大截图数量
        self.last_cleanup_time = 0      # 上次清理时间
        self.cleanup_interval = 3600    # 清理间隔(秒)，默认1小时
        
        self.ocr = PaddleOCR(use_angle_cls=True, lang="ch", use_gpu=False)
        self.connection_manager = ConnectionManager()
        self.device_id = self.select_device()
        if self.device_id:
            self.connection_manager.register_device(self.device_id)
            self.get_device_resolution(self.device_id)
            self.get_ballery_level(self.device_id)
        self.get_current_hour()
        logging.disable(logging.DEBUG)
        logging.disable(logging.WARNING)

    def select_device(self):
        """选择需要连接的设备"""
        # 检测是否为树莓派环境
        is_raspberry_pi = False
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
                is_raspberry_pi = 'Raspberry Pi' in cpuinfo or 'BCM' in cpuinfo
        except:
            # 检查系统架构
            try:
                arch = subprocess.check_output(['uname', '-m']).decode('utf-8').strip()
                is_raspberry_pi = 'arm' in arch.lower()
            except:
                pass
        
        # 获取已连接设备列表
        devicelist = self.connection_manager.get_connected_devices()
        devicenum = len(devicelist)
        
        # 如果是树莓派环境且没有设备连接，自动尝试无线连接
        if is_raspberry_pi and devicenum == 0:
            print("检测到树莓派环境，尝试无线连接...")
            
            # 尝试读取保存的设备IP地址
            ip_file = os.path.join(os.path.dirname(__file__), 'device_ip.txt')
            if os.path.exists(ip_file):
                try:
                    with open(ip_file, 'r') as f:
                        device_info = f.read().strip().split(':')
                        ip_address = device_info[0]
                        port = 5555
                        if len(device_info) > 1:
                            port = int(device_info[1])
                        
                    print(f"尝试连接到上次使用的设备: {ip_address}:{port}")
                    
                    # 尝试连接
                    try:
                        # 先断开可能存在的连接
                        subprocess.run(['adb', 'disconnect', f'{ip_address}:{port}'], check=False)
                        time.sleep(1)
                        
                        # 连接到设备
                        connect_result = subprocess.run(['adb', 'connect', f'{ip_address}:{port}'], 
                                                      capture_output=True, text=True, check=False)
                        
                        if "connected" in connect_result.stdout.lower() and "failed" not in connect_result.stdout.lower():
                            print(f"成功连接到设备: {ip_address}:{port}")
                            
                            # 验证连接
                            time.sleep(1)
                            devicelist = self.connection_manager.get_connected_devices()
                            if f"{ip_address}:{port}" in devicelist:
                                print("无线连接验证成功！")
                                self.connection_manager.register_device(f"{ip_address}:{port}", ip_address, port)
                                return f"{ip_address}:{port}"
                    except Exception as e:
                        print(f"连接失败: {str(e)}")
                except Exception as e:
                    print(f"读取设备信息失败: {str(e)}")
            
            # 如果没有保存的设备信息或连接失败，要求用户输入
            print("\n请输入手机IP地址(例如: *************):")
            ip_address = input().strip()
            
            if not ip_address:
                print("未提供IP地址，无法连接设备")
                return False
                
            print("请输入端口号(默认5555):")
            port_input = input().strip()
            port = 5555
            if port_input:
                try:
                    port = int(port_input)
                except ValueError:
                    print("端口号必须是数字，使用默认端口5555")
            
            # 尝试连接
            try:
                # 先断开可能存在的连接
                subprocess.run(['adb', 'disconnect', f'{ip_address}:{port}'], check=False)
                time.sleep(1)
                
                # 连接到设备
                connect_result = subprocess.run(['adb', 'connect', f'{ip_address}:{port}'], 
                                              capture_output=True, text=True, check=False)
                
                if "connected" in connect_result.stdout.lower() and "failed" not in connect_result.stdout.lower():
                    print(f"成功连接到设备: {ip_address}:{port}")
                    
                    # 保存设备信息以便下次使用
                    try:
                        with open(ip_file, 'w') as f:
                            f.write(f"{ip_address}:{port}")
                        print(f"设备信息已保存，下次将自动连接")
                    except Exception as e:
                        print(f"保存设备信息失败: {str(e)}")
                    
                    # 验证连接
                    time.sleep(1)
                    devicelist = self.connection_manager.get_connected_devices()
                    if f"{ip_address}:{port}" in devicelist:
                        print("无线连接验证成功！")
                        self.connection_manager.register_device(f"{ip_address}:{port}", ip_address, port)
                        return f"{ip_address}:{port}"
                    else:
                        print("无线连接验证失败")
                else:
                    print(f"连接失败: {connect_result.stdout}")
            except Exception as e:
                print(f"连接失败: {str(e)}")
            
            return False
        
        # 处理常规情况
        if devicenum == 0:
            print("当前无设备连接电脑,请检查设备连接情况!")
            
            # 非树莓派环境也提供无线连接选项
            print("是否要尝试通过无线方式连接设备？(y/n)")
            choice = input().lower()
            if choice == 'y' or choice == 'yes':
                print("请输入设备的IP地址:")
                ip_address = input().strip()
                if not ip_address:
                    print("IP地址不能为空")
                    return False
                    
                print("请输入端口号(默认5555):")
                port_input = input().strip()
                port = 5555
                if port_input:
                    try:
                        port = int(port_input)
                    except ValueError:
                        print("端口号必须是数字，使用默认端口5555")
                
                # 尝试连接
                try:
                    # 先断开可能存在的连接
                    subprocess.run(['adb', 'disconnect', f'{ip_address}:{port}'], check=False)
                    time.sleep(1)
                    
                    # 连接到设备
                    connect_result = subprocess.run(['adb', 'connect', f'{ip_address}:{port}'], 
                                                  capture_output=True, text=True, check=False)
                    
                    if "connected" in connect_result.stdout.lower() and "failed" not in connect_result.stdout.lower():
                        print(f"成功连接到设备: {ip_address}:{port}")
                        
                        # 验证连接
                        time.sleep(1)
                        devicelist = self.connection_manager.get_connected_devices()
                        if f"{ip_address}:{port}" in devicelist:
                            print("无线连接验证成功！")
                            self.connection_manager.register_device(f"{ip_address}:{port}", ip_address, port)
                            return f"{ip_address}:{port}"
                        else:
                            print("无线连接验证失败")
                    else:
                        print(f"连接失败: {connect_result.stdout}")
                except Exception as e:
                    print(f"连接失败: {str(e)}")
                
                return False
            else:
                return False
        elif devicenum == 1:
            print("当前有一台设备连接，编号:%s." % devicelist[0])
            return devicelist[0]
        else:
            print("当前存在多台设备连接! 输入数字选择对应设备:")
            dictdevice = {}
            for i in range(devicenum):
                try:
                    string = subprocess.Popen("adb -s %s shell getprop ro.product.device" % devicelist[i], shell=True,
                                              stdout=subprocess.PIPE)
                    modestring = string.stdout.read().strip()  # 去除掉自动生成的回车
                    print("%s:%s---%s" % (i + 1, devicelist[i], modestring))
                except:
                    print("%s:%s" % (i + 1, devicelist[i]))
                dictdevice[i + 1] = devicelist[i]
            
            # 添加无线连接选项
            print("%s:通过IP地址连接无线设备" % (devicenum + 1))
            
            try:
                num = input()
                num = int(num)
                if num in dictdevice.keys():
                    return dictdevice[num]
                elif num == devicenum + 1:
                    # 用户选择了无线连接选项
                    print("请输入设备的IP地址:")
                    ip_address = input().strip()
                    if not ip_address:
                        print("IP地址不能为空")
                        return False
                        
                    print("请输入端口号(默认5555):")
                    port_input = input().strip()
                    port = 5555
                    if port_input:
                        try:
                            port = int(port_input)
                        except ValueError:
                            print("端口号必须是数字，使用默认端口5555")
                    
                    # 尝试连接
                    try:
                        # 先断开可能存在的连接
                        subprocess.run(['adb', 'disconnect', f'{ip_address}:{port}'], check=False)
                        time.sleep(1)
                        
                        # 连接到设备
                        connect_result = subprocess.run(['adb', 'connect', f'{ip_address}:{port}'], 
                                                      capture_output=True, text=True, check=False)
                        
                        if "connected" in connect_result.stdout.lower() and "failed" not in connect_result.stdout.lower():
                            print(f"成功连接到设备: {ip_address}:{port}")
                            
                            # 验证连接
                            time.sleep(1)
                            devicelist = self.connection_manager.get_connected_devices()
                            if f"{ip_address}:{port}" in devicelist:
                                print("无线连接验证成功！")
                                self.connection_manager.register_device(f"{ip_address}:{port}", ip_address, port)
                                return f"{ip_address}:{port}"
                            else:
                                print("无线连接验证失败")
                        else:
                            print(f"连接失败: {connect_result.stdout}")
                    except Exception as e:
                        print(f"连接失败: {str(e)}")
                    
                    return False
                else:
                    print('输入不正确，请重新运行程序')
                    return False
            except ValueError:
                print('输入不正确，请重新运行程序')
                return False

    def ensure_device_connected(self, device_id):
        """确保设备连接状态，如果断开则尝试重连"""
        if not self.connection_manager.check_device_connection(device_id):
            print(f"设备 {device_id} 连接已断开，尝试重新连接")
            return self.connection_manager.reconnect_device(device_id)
        return True
            
    def execute_adb_command(self, command, device_id=None):
        """执行ADB命令并处理潜在的连接问题"""
        if device_id is None:
            device_id = self.device_id
            
        # 限制命令执行频率，避免过于频繁
        current_time = time.time()
        if current_time - self.last_cmd_time < self.min_cmd_interval:
            time.sleep(self.min_cmd_interval - (current_time - self.last_cmd_time))
        self.last_cmd_time = time.time()

        # 定期检查连接状态
        self.connection_manager.periodic_connection_check()
        
        # 确保设备仍然连接
        if not self.ensure_device_connected(device_id):
            raise ConnectionError(f"设备 {device_id} 无法连接")

        try:
            # 执行命令
            return subprocess.run(command, shell=isinstance(command, str), 
                           check=True, capture_output=True, text=True)
        except subprocess.SubprocessError as e:
            print(f"执行命令失败: {str(e)}")
            # 尝试重连设备
            if self.connection_manager.reconnect_device(device_id):
                # 重试命令
                return subprocess.run(command, shell=isinstance(command, str), 
                               check=True, capture_output=True, text=True)
            raise

    def get_screenshot(self, device_id, path='pic'):
        """截图3个adb命令需要2S左右的时间"""
        # 定期清理截图文件
        self.cleanup_screenshots(path)
        
        if path == 'pic':
            path = os.path.dirname(__file__) + '/pic'
        else:
            path = os.path.dirname(__file__) + '/target_pic'
        if not os.path.exists(path):
            os.makedirs(path)
            
        # 最大重试次数
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 确保设备连接正常
                if not self.connection_manager.check_device_connection(device_id):
                    print(f"设备 {device_id} 连接已断开，尝试重新连接")
                    if not self.connection_manager.reconnect_device(device_id):
                        print(f"重新连接设备 {device_id} 失败，等待5秒后重试")
                        time.sleep(5)
                        retry_count += 1
                        continue
                
                # 使用完整路径并正确处理命令参数
                subprocess.run(['adb', '-s', device_id, 'shell', 'screencap', '-p', '/sdcard/DCIM/screenshot.png'], 
                               check=True, timeout=self.screenshot_timeout)
                subprocess.run(['adb', '-s', device_id, 'pull', '/sdcard/DCIM/screenshot.png', path], 
                               check=True, timeout=self.screenshot_timeout)
                timetag = datetime.now().strftime('%H:%M:%S')
                print("{} 获取屏幕截图".format(timetag))
                return True
            except subprocess.CalledProcessError as e:
                print(f"截图命令执行失败: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"等待2秒后重试截图 ({retry_count}/{max_retries})")
                    time.sleep(2)
            except subprocess.TimeoutExpired as e:
                print(f"截图命令超时: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"等待3秒后重试截图 ({retry_count}/{max_retries})")
                    time.sleep(3)
            except Exception as e:
                print(f"截图时发生未知错误: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    print(f"等待5秒后重试截图 ({retry_count}/{max_retries})")
                    time.sleep(5)
        
        # 所有重试都失败后，尝试一种备用方法
        try:
            print("尝试使用备用方法获取截图")
            subprocess.run(['adb', '-s', device_id, 'shell', 'screencap', '-p', '/sdcard/DCIM/screenshot1.png'], 
                           check=True, timeout=self.screenshot_timeout)
            subprocess.run(['adb', '-s', device_id, 'shell', 'mv', '/sdcard/DCIM/screenshot1.png', '/sdcard/DCIM/screenshot.png'], 
                           check=True, timeout=self.screenshot_timeout)
            subprocess.run(['adb', '-s', device_id, 'pull', '/sdcard/DCIM/screenshot.png', path], 
                           check=True, timeout=self.screenshot_timeout)
            return True
        except Exception as e:
            print(f"备用截图方法也失败: {str(e)}")
            return False

    def get_current_hour(self):
        """获取当前的时间小时数"""
        time_hour = datetime.now().strftime('%H')
        print("当前时间{}点".format(time_hour))
        return int(time_hour)

    def click_confirm(self, device_id):
        """点击确认键"""
        os.system("adb -s %s shell input keyevent 66" % device_id)
        print("点击确认键")

    def click_back(self, device_id):
        """点击返回键"""
        os.system("adb -s %s shell input keyevent 4" % device_id)
        print("点击返回键")



    def send_text_with_adbkeyboard(self, device_id, safe_mode=False):
        """
        使用adbkeyboard发送文本消息
        基于实验发现：系统会自动关闭adbkeyboard输入法，无需手动关闭

        参数:
        device_id: 设备ID
        safe_mode: 保留参数以保持兼容性，但已不需要

        返回:
        True: 发送成功
        False: 发送失败
        """
        try:
            print("使用adbkeyboard发送文本...")

            # 发送回车键 (adbkeyboard的标准发送方式)
            enter_cmd = f'adb -s {device_id} shell input keyevent 66'  # KEYCODE_ENTER
            result = subprocess.run(enter_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                print("✅ 回车键发送成功")
                # 🎉 实验证明：系统会自动关闭adbkeyboard输入法，无需手动操作！
                time.sleep(1.0)  # 等待系统自动关闭输入法
                print("✅ 系统自动关闭输入法")
                return True
            else:
                print("❌ 回车键发送失败")
                return False

        except Exception as e:
            print(f"adbkeyboard发送失败: {str(e)}")
            return False

    def hide_adbkeyboard(self, device_id):
        """
        关闭adbkeyboard输入法界面
        使用安全的方法关闭，避免在直播间中误触返回键退出

        参数:
        device_id: 设备ID

        返回:
        True: 关闭成功
        False: 关闭失败
        """
        try:
            print("关闭adbkeyboard界面...")

            # 方法1: 尝试使用ESC键关闭输入法（更安全）
            esc_cmd = f'adb -s {device_id} shell input keyevent 111'  # KEYCODE_ESCAPE
            result = subprocess.run(esc_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                print("✅ ESC键关闭adbkeyboard成功")
                time.sleep(0.5)
                return True
            else:
                print("❌ ESC键关闭adbkeyboard失败，尝试其他方法")

            # 方法2: 尝试切换到系统默认输入法
            try:
                # 获取默认输入法
                ime_cmd = f'adb -s {device_id} shell settings get secure default_input_method'
                ime_result = subprocess.run(ime_cmd, shell=True, capture_output=True, text=True, timeout=5)

                if ime_result.returncode == 0 and ime_result.stdout.strip():
                    default_ime = ime_result.stdout.strip()
                    # 如果当前不是默认输入法，切换回去
                    if 'adbkeyboard' not in default_ime.lower():
                        switch_cmd = f'adb -s {device_id} shell ime set {default_ime}'
                        switch_result = subprocess.run(switch_cmd, shell=True, capture_output=True, text=True, timeout=5)
                        if switch_result.returncode == 0:
                            print("✅ 切换到默认输入法成功")
                            time.sleep(0.5)
                            return True
            except Exception as e:
                print(f"切换输入法失败: {e}")

            # 方法3: 发送空白点击来关闭输入法（点击输入框外的区域）
            try:
                # 点击屏幕上方的空白区域来关闭输入法
                click_cmd = f'adb -s {device_id} shell input tap 540 100'  # 点击屏幕顶部中央
                click_result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True, timeout=5)
                if click_result.returncode == 0:
                    print("✅ 点击空白区域关闭输入法成功")
                    time.sleep(0.5)
                    return True
            except Exception as e:
                print(f"点击空白区域失败: {e}")

            print("⚠️ 所有关闭adbkeyboard的方法都失败了，输入法可能仍然显示")
            return False

        except Exception as e:
            print(f"关闭adbkeyboard失败: {str(e)}")
            return False

    def hide_keyboard_safe_for_live_room(self, device_id):
        """
        专门用于直播间的安全关闭输入法方法
        绝对不使用返回键，避免退出直播间

        参数:
        device_id: 设备ID

        返回:
        True: 关闭成功
        False: 关闭失败
        """
        try:
            print("使用直播间安全方法关闭输入法...")

            # 方法1: 点击直播间的空白区域（避开所有按钮）
            # 选择直播间中央偏上的安全区域
            safe_x = 540  # 屏幕中央
            safe_y = 800  # 直播画面区域，避开底部按钮

            click_cmd = f'adb -s {device_id} shell input tap {safe_x} {safe_y}'
            result = subprocess.run(click_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                print("✅ 点击直播间安全区域关闭输入法成功")
                time.sleep(1.0)  # 等待输入法关闭
                return True
            else:
                print("❌ 点击直播间安全区域失败")

            # 方法2: 使用ESC键
            esc_cmd = f'adb -s {device_id} shell input keyevent 111'  # KEYCODE_ESCAPE
            esc_result = subprocess.run(esc_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if esc_result.returncode == 0:
                print("✅ ESC键关闭输入法成功")
                time.sleep(0.5)
                return True
            else:
                print("❌ ESC键关闭输入法失败")

            # 方法3: 发送MENU键（通常不会影响应用状态）
            menu_cmd = f'adb -s {device_id} shell input keyevent 82'  # KEYCODE_MENU
            menu_result = subprocess.run(menu_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if menu_result.returncode == 0:
                print("✅ MENU键操作成功")
                time.sleep(0.5)
                # 再次点击空白区域确保输入法关闭
                subprocess.run(click_cmd, shell=True, capture_output=True, text=True, timeout=5)
                return True

            print("⚠️ 直播间安全关闭输入法的所有方法都失败了")
            return False

        except Exception as e:
            print(f"直播间安全关闭输入法失败: {str(e)}")
            return False

    def send_text_with_adbkeyboard_experiment(self, device_id):
        """
        实验版本：使用adbkeyboard发送文本消息
        发送完成后不做任何关闭操作，观察系统是否自动关闭输入法

        参数:
        device_id: 设备ID

        返回:
        True: 发送成功
        False: 发送失败
        """
        try:
            print("🧪 实验模式：使用adbkeyboard发送文本，不关闭输入法...")

            # 1. 发送回车键 (adbkeyboard的标准发送方式)
            enter_cmd = f'adb -s {device_id} shell input keyevent 66'  # KEYCODE_ENTER
            result = subprocess.run(enter_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                print("✅ 回车键发送成功")
                print("🧪 实验：等待5秒，观察输入法是否自动关闭...")
                time.sleep(5.0)

                print("🧪 实验完成：请观察手机屏幕上的输入法状态")
                print("   - 如果输入法已自动关闭，说明系统会自动处理")
                print("   - 如果输入法仍然显示，说明需要手动关闭")

                return True
            else:
                print("❌ 回车键发送失败")
                return False

        except Exception as e:
            print(f"实验版adbkeyboard发送失败: {str(e)}")
            return False

    def input_text_via_adbkeyboard(self, device_id, text):
        """
        通过ADB广播方式向标准adbkeyboard输入法发送中文文本
        这是纯ADB方案，无需任何Python依赖

        注意：需要使用标准版本的AdbKeyboard (com.android.adbkeyboard)
        下载地址：https://github.com/senzhk/ADBKeyBoard/releases

        参数:
        device_id: 设备ID
        text: 要输入的文本内容

        返回:
        True: 输入成功
        False: 输入失败
        """
        try:
            print(f"使用ADB广播方式输入文本: {text}")

            # 方法1: 使用标准AdbKeyboard的广播格式
            # 对包含空格的文本进行特殊处理
            if ' ' in text:
                print("检测到空格字符，使用Base64编码方案...")
                broadcast_cmd = f'adb -s {device_id} shell am broadcast -a ADB_INPUT_B64 --es msg "{self._encode_base64(text)}"'
            else:
                broadcast_cmd = f'adb -s {device_id} shell am broadcast -a ADB_INPUT_TEXT --es msg "{text}"'

            result = subprocess.run(broadcast_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print("✅ ADB广播发送成功")
                time.sleep(1.0)  # 等待输入完成
                return True
            else:
                print(f"❌ ADB广播发送失败: {result.stderr}")

                # 方法2: 备用方案 - 使用Base64编码
                print("尝试Base64编码方案...")
                backup_cmd = f'adb -s {device_id} shell am broadcast -a ADB_INPUT_B64 --es msg "{self._encode_base64(text)}"'
                backup_result = subprocess.run(backup_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if backup_result.returncode == 0:
                    print("✅ Base64广播发送成功")
                    time.sleep(1.0)
                    return True
                else:
                    print(f"❌ Base64广播也失败: {backup_result.stderr}")

                    # 方法3: 最后备用方案 - 使用Android input text命令
                    print("尝试Android input text命令...")
                    return self._input_text_char_by_char(device_id, text)

        except Exception as e:
            print(f"ADB广播输入失败: {str(e)}")
            return False

    def _encode_base64(self, text):
        """将文本编码为base64格式，用于adbkeyboard的base64广播"""
        import base64
        try:
            encoded = base64.b64encode(text.encode('utf-8')).decode('ascii')
            return encoded
        except Exception as e:
            print(f"Base64编码失败: {e}")
            return ""

    def _input_text_char_by_char(self, device_id, text):
        """
        逐字符输入方案 - 使用Android input text命令
        这是最可靠的文本输入方法，直接调用Android系统的输入功能
        """
        try:
            print("使用Android input text命令输入...")

            # 使用Android的input text命令，这是最直接可靠的方法
            # 需要对特殊字符进行转义
            escaped_text = text.replace('"', '\\"').replace("'", "\\'").replace(" ", "\\ ")
            input_cmd = f'adb -s {device_id} shell input text "{escaped_text}"'

            print(f"执行命令: {input_cmd}")
            result = subprocess.run(input_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print(f"✅ 文本输入成功: {text}")
                time.sleep(0.5)  # 等待输入完成
                return True
            else:
                print(f"❌ 文本输入失败: {result.stderr}")

                # 备用方案：逐字符使用input text
                print("尝试逐字符输入...")
                success_count = 0

                for i, char in enumerate(text):
                    # 对特殊字符进行转义
                    escaped_char = char.replace('"', '\\"').replace("'", "\\'").replace(" ", "\\ ")
                    char_cmd = f'adb -s {device_id} shell input text "{escaped_char}"'

                    char_result = subprocess.run(char_cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if char_result.returncode == 0:
                        success_count += 1
                        time.sleep(0.1)  # 短暂延迟避免输入过快
                    else:
                        print(f"字符 '{char}' 输入失败")

                if success_count == len(text):
                    print(f"✅ 逐字符输入完成，成功输入 {success_count}/{len(text)} 个字符")
                    return True
                else:
                    print(f"⚠️ 部分字符输入失败，成功 {success_count}/{len(text)} 个字符")
                    return success_count > 0

        except Exception as e:
            print(f"文本输入失败: {str(e)}")
            return False

    def clear_input_field_via_adbkeyboard(self, device_id):
        """
        使用ADB广播方式清空输入框
        提供多种清空方案确保可靠性

        参数:
        device_id: 设备ID

        返回:
        True: 清空成功
        False: 清空失败
        """
        try:
            print("清空输入框...")

            # 方法1: 使用AdbKeyboard的清空广播
            clear_cmd = f'adb -s {device_id} shell am broadcast -a ADB_CLEAR_TEXT'
            result = subprocess.run(clear_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                print("✅ ADB清空广播发送成功")
                time.sleep(0.5)
                return True
            else:
                print(f"❌ ADB清空广播失败: {result.stderr}")

                # 方法2: 使用全选+删除的组合键
                print("尝试全选+删除方案...")
                # 全选 (Ctrl+A)
                select_cmd = f'adb -s {device_id} shell input keyevent 29'
                subprocess.run(select_cmd, shell=True, capture_output=True, text=True, timeout=3)
                time.sleep(0.3)

                # 删除 (Delete)
                delete_cmd = f'adb -s {device_id} shell input keyevent 67'
                delete_result = subprocess.run(delete_cmd, shell=True, capture_output=True, text=True, timeout=3)

                if delete_result.returncode == 0:
                    print("✅ 全选+删除成功")
                    time.sleep(0.5)
                    return True
                else:
                    print("❌ 全选+删除失败")

                    # 方法3: 多次按删除键
                    print("尝试多次删除方案...")
                    for i in range(20):  # 最多删除20个字符
                        backspace_cmd = f'adb -s {device_id} shell input keyevent 67'
                        subprocess.run(backspace_cmd, shell=True, capture_output=True, text=True, timeout=2)
                        time.sleep(0.05)

                    print("✅ 多次删除完成")
                    return True

        except Exception as e:
            print(f"清空输入框失败: {str(e)}")
            return False

    def input_text_chinese(self, device_id, text):
        """
        输入中文文本 - 使用纯ADB广播方案

        完全基于ADB命令，使用标准AdbKeyboard的ADB广播方式
        支持中文、英文、数字、特殊字符和空格字符
        自动处理空格字符（使用Base64编码）

        参数:
        device_id: 设备ID
        text: 要输入的文本内容

        返回:
        True: 输入成功
        False: 输入失败
        """
        print("🚀 使用纯ADB广播方案输入文本...")
        return self.input_text_via_adbkeyboard(device_id, text)

    # 为了向后兼容，保留原有的方法名
    def send_text_message(self, device_id):
        """向后兼容的方法，现在调用adbkeyboard方案"""
        return self.send_text_with_adbkeyboard(device_id)

    def get_ballery_level(self, device_id):
        """获取设备电量信息"""
        try:
            command = f"adb -s {device_id} shell dumpsys battery"
            result = self.execute_adb_command(command)
            battery_info_string = result.stdout
            location = re.search('level:', battery_info_string)
            if not location:
                return 100  # 默认返回满电量
                
            span = location.span()
            start, end = span
            start = end + 1
            for i in range(5):
                end += 1
                if end >= len(battery_info_string) or battery_info_string[end] == "\n":
                    break
            battery_level = battery_info_string[start:end].strip()  # 第几个到第几个中间接冒号
            battery_level = int(battery_level)
            print("设备当前电量为{}".format(battery_level))
            return battery_level
        except Exception as e:
            print(f"获取电量失败: {str(e)}")
            return 100  # 默认返回满电量

    def get_device_resolution(self, device_id):
        """获取设备分辨率"""
        try:
            command = f"adb -s {device_id} shell wm size"
            result = self.execute_adb_command(command)
            resolution_info_string = result.stdout
            match = re.search(r'Physical size: (\d+)x(\d+)', resolution_info_string)
            if match:
                width = int(match.group(1))
                height = int(match.group(2))
                print("设备分辨率为：{}*{}".format(width, height))
                return width, height
            else:
                print("未能识别设备分辨率，使用默认值 1080*2280")
                return 1080, 2280
        except Exception as e:
            print(f"获取分辨率失败: {str(e)}，使用默认值 1080*2280")
            return 1080, 2280

    def calculate_coordinate(self, x, y, base_width=1080, base_height=2280):
        """
        根据基准分辨率计算实际设备上的坐标位置
        
        参数:
        x, y: 基准分辨率下的坐标
        base_width, base_height: 基准分辨率，默认为1080*2280
        
        返回:
        实际设备上的坐标位置
        """
        # 确保已获取设备分辨率
        if not hasattr(self, 'resolution_ratio_x') or not hasattr(self, 'resolution_ratio_y'):
            if hasattr(self, 'device_id'):
                self.resolution_ratio_x, self.resolution_ratio_y = self.get_device_resolution(self.device_id)
            else:
                raise ValueError("设备ID未设置，无法获取分辨率")
        
        # 计算实际坐标
        actual_x = x * self.resolution_ratio_x // base_width
        actual_y = y * self.resolution_ratio_y // base_height
        
        return actual_x, actual_y

    def save_reward_pic(self, device_id):
        path = os.path.dirname(__file__) + '/pic'
        timepic = datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
        subprocess.Popen(
            'adb  -s %s shell screencap -p /sdcard/DCIM/screenshot.png' % (
                device_id)).wait()  # 截图获奖的界面
        subprocess.Popen(
            'adb  -s %s pull /sdcard/DCIM/screenshot.png %s/%s.png ' % (device_id, path, timepic),
            stdout=subprocess.PIPE).wait()
        subprocess.Popen('adb  -s %s shell rm /sdcard/DCIM/screenshot.png' % device_id).wait()
        print("中奖了，点击领奖，保存中奖图片{}.png".format(timepic))

    def cut_pic(self, left_up=(0, 63), right_down=(1080, 1620), target='', name=''):
        '''裁剪图片，获取需要的区域小图片方便识别'''
        if target == '' or target == False:
            path = os.path.dirname(__file__) + '/pic'
            pic1_path = path + '/screenshot.png'
            pic = Image.open(pic1_path)
            if name == '':
                cut_pic_path = path + '/cut.png'
            else:
                cut_pic_path = path + '/' + name + '.png'
            pic.crop((left_up[0], left_up[1], right_down[0], right_down[1])).save(cut_pic_path)
            return True
        path_target = os.path.dirname(__file__) + '/pic/' + target
        pic1_path = path_target + '/screenshot.png'
        pic = Image.open(pic1_path)
        if name == '':
            cut_pic_path = path_target + '/cut.png'
        else:
            cut_pic_path = path_target + '/' + name + '.png'
        pic.crop((left_up[0], left_up[1], right_down[0], right_down[1])).save(cut_pic_path)

    def analyse_pic_word(self, picname='', change_color=0):
        """识别图像中的文字, change_color=1或2为不同的二值化模式，其他不做处理"""
        path = os.path.dirname(__file__) + '/pic'
        if picname == '':
            pic = path + '/cut.png'
        else:
            pic = path + '/' + picname + '.png'
        img = Image.open(pic)
        img = img.convert('L')  # 转换为灰度图
        if change_color == 1:
            img = img.point(lambda x: 0 if x < 128 else 255)  # 二值化
        elif change_color == 2:
            img = img.point(lambda x: 0 if x < 180 else 255)  # 二值化
        # img.save(pic)
        # img.show()
        img_np = np.array(img)  # 将 Image 对象转换为 numpy 数组
        result = self.ocr.ocr(img_np)
        if result == [None]:
            return ''
        return self.extract_ocr_content(result)

    def extract_ocr_content(self, content=[]):
        """对OCR识别到的内容进行取值和拼接，变成完整的一段内容"""
        ocr_result = content
        extracted_content = []
        for item in ocr_result[0]:  # item 的结构为 [位置信息, (识别内容, 置信度)]
            extracted_content.append(item[1][0])
        contains = ''.join(context for context in extracted_content if context)
        # print(contain)
        return contains

    def delay(self, seconds=1):
        time.sleep(seconds)

    def random_delay(self, min_seconds=1, max_seconds=100):
        """等待随机的时长"""
        random_wait_seconds = random.randint(min_seconds, max_seconds)
        print("随机等待{}秒.".format(random_wait_seconds))
        self.delay(random_wait_seconds)

    def check_countdown(self, last_time=''):
        """对倒计时时间进行转化，变成秒存储"""
        try:
            last_time = ''.join([char for char in last_time if char.isdigit()])
            if len(last_time) == 4:
                # minutes, seconds = map(int, last_time.split(':'))
                minutes = int(last_time[:2])
                seconds = int(last_time[2:])
            else:
                print("时间格式异常")
                return False
            # 转换为总秒数
            total_seconds = minutes * 60 + seconds
            print("剩余总秒数：", total_seconds)
            if total_seconds > 900:  # 如果识别到的分钟大于15，说明识别异常了，按15分钟处理
                total_seconds = 890
            # datetime.strptime(in_time, '%H:%M')
            # time_obj = datetime.strptime(in_time, '%H:%M')
            # print("转换后的时间格式：", time_obj)
            now = datetime.now()
            future_time = now + timedelta(seconds=total_seconds)
            # 将到期时间转换为时间戳
            future_timestamp = future_time.timestamp()
            future_datetime = datetime.fromtimestamp(future_timestamp)
            # 将datetime对象格式化为通常的时间格式
            formatted_future_time = future_datetime.strftime('%Y-%m-%d %H:%M:%S')
            print("预计开奖时间：", formatted_future_time)
            return total_seconds, future_timestamp
        except ValueError:
            print("输入的字符串不是有效的时间格式")
            return False

    def swipe(self, device_id, left_up_x=0, left_up_y=0, right_down_x=1080, right_down_y=1500, steps=200):
        """划动屏幕"""
        os.system("adb -s {} shell input swipe {} {} {} {} {}".format(device_id, left_up_x, left_up_y, right_down_x, right_down_y, steps))

    def swipe_up(self, device_id=None, duration=200, distance=None, start_x_offset=0, start_y_offset=0):
        """
        使用贝塞尔曲线生成更自然的上划轨迹
        
        参数:
        device_id: 设备ID，如果为None则使用当前设备
        duration: 滑动持续时间(毫秒)
        distance: 滑动距离，如果为None则使用默认距离(屏幕高度的2/3)
        start_x_offset: 起始点X轴随机偏移范围(像素)
        start_y_offset: 起始点Y轴随机偏移范围(像素)
        """
        if device_id is None:
            device_id = self.device_id
            
        # 获取屏幕分辨率
        width, height = self.get_device_resolution(device_id)
        
        # 设置默认滑动距离为屏幕高度的2/3
        if distance is None:
            distance = int(height * 0.67)
            
        # 计算起始点和结束点，添加随机偏移
        center_x = width // 2
        start_x = center_x + random.randint(-start_x_offset, start_x_offset) if start_x_offset > 0 else center_x
        start_y = int(height * 0.7) + random.randint(-start_y_offset, start_y_offset) if start_y_offset > 0 else int(height * 0.7)
        end_y = start_y - distance
        
        # 使用基本的swipe命令执行滑动
        # 在实际应用中，可以实现贝塞尔曲线轨迹，但需要使用sendevent命令序列
        # 这里使用简化版本，通过随机化参数模拟自然滑动
        actual_duration = random.randint(int(duration * 0.8), int(duration * 1.2))  # 随机化持续时间
        
        print(f"执行上划操作: 从({start_x}, {start_y})到({start_x}, {end_y}), 持续{actual_duration}ms")
        self.swipe(device_id, start_x, start_y, start_x, end_y, actual_duration)
        
        # 添加随机延迟，模拟人类操作间隔
        delay_time = random.uniform(0.5, 2.0)
        self.delay(delay_time)

    def swipe_up_bezier(self, device_id=None, duration=500, distance=None, control_points=2):
        """
        使用贝塞尔曲线生成更自然的上划轨迹，并模拟60fps的滑动速度
        
        参数:
        device_id: 设备ID，如果为None则使用当前设备
        duration: 滑动持续时间(毫秒)
        distance: 滑动距离，如果为None则使用默认距离(屏幕高度的2/3)
        control_points: 贝塞尔曲线控制点数量，影响曲线复杂度
        """
        if device_id is None:
            device_id = self.device_id
            
        # 获取屏幕分辨率
        width, height = self.get_device_resolution(device_id)
        
        # 设置默认滑动距离为屏幕高度的2/3
        if distance is None:
            distance = int(height * 0.67)
            
        # 计算起始点和结束点，添加随机偏移
        center_x = width // 2
        start_x = center_x + random.randint(-30, 30)
        start_y = int(height * 0.7) + random.randint(-20, 20)
        end_x = center_x + random.randint(-30, 30)
        end_y = start_y - distance
        
        # 生成贝塞尔曲线控制点
        control_xs = []
        control_ys = []
        for i in range(control_points):
            # 控制点X坐标在起点和终点X坐标附近随机
            control_x = random.randint(min(start_x, end_x) - 50, max(start_x, end_x) + 50)
            # 控制点Y坐标在起点和终点Y坐标之间随机
            progress = (i + 1) / (control_points + 1)
            mid_y = start_y - progress * distance
            control_y = int(mid_y + random.randint(-50, 50))
            control_xs.append(control_x)
            control_ys.append(control_y)
            
        print(f"执行贝塞尔曲线上划: 从({start_x}, {start_y})到({end_x}, {end_y}), 持续{duration}ms")
        
        # 使用单次滑动命令执行整个轨迹，而不是分段执行
        # 这样可以避免系统将多段滑动识别为多次独立的上划操作
        try:
            # 使用标准的Android滑动命令
            self.swipe(device_id, start_x, start_y, end_x, end_y, duration)
            
            # 添加随机延迟，模拟人类操作间隔
            delay_time = random.uniform(0.8, 2.5)
            self.delay(delay_time)
            
        except Exception as e:
            print(f"执行贝塞尔曲线上划时出错: {str(e)}")
            # 如果出错，尝试使用普通上划
            self.swipe_up(device_id, duration=duration)
        
    def _bezier_curve(self, t, p0, control_points, p3):
        """
        计算贝塞尔曲线上的点
        
        参数:
        t: 参数，范围[0,1]
        p0: 起点
        control_points: 控制点列表
        p3: 终点
        
        返回:
        贝塞尔曲线上对应t值的坐标
        """
        n = len(control_points) + 1  # 曲线阶数
        result = (1 - t) ** n * p0  # 起点项
        
        # 控制点项
        for i, p in enumerate(control_points):
            coef = self._combination(n, i + 1) * (1 - t) ** (n - i - 1) * t ** (i + 1)
            result += coef * p
            
        # 终点项
        result += t ** n * p3
        
        return result
        
    def _combination(self, n, k):
        """
        计算组合数 C(n,k)
        """
        return math.factorial(n) // (math.factorial(k) * math.factorial(n - k))

    def click(self, device_id, x=500, y=500):
        """点击坐标位置"""
        os.system(
            "adb -s {} shell input tap {} {}".format(device_id, x, y))

    def get_device_ip(self, device_id):
        """获取设备的IP地址"""
        try:
            result = subprocess.run(['adb', '-s', device_id, 'shell', 'ip', 'addr', 'show', 'wlan0'], 
                                  capture_output=True, text=True, check=True)
            # 使用正则表达式匹配IP地址
            ip_match = re.search(r'inet (\d+\.\d+\.\d+\.\d+)', result.stdout)
            if ip_match:
                ip_address = ip_match.group(1)
                print(f"设备IP地址: {ip_address}")
                return ip_address
            else:
                print("无法获取设备IP地址")
                return None
        except subprocess.CalledProcessError as e:
            print(f"获取IP地址失败: {str(e)}")
            return None

    def setup_wireless_connection(self, device_id, port=5555):
        """设置无线连接"""
        try:
            # 获取设备IP地址
            ip_address = self.get_device_ip(device_id)
            if not ip_address:
                print("无法设置无线连接：未获取到IP地址")
                return False

            # 设置ADB无线连接
            command = ['adb', '-s', device_id, 'tcpip', str(port)]
            self.execute_adb_command(command)
            print(f"ADB TCP/IP模式已启用，端口: {port}")
            
            # 等待端口启动
            time.sleep(2)
            
            # 连接到设备
            connect_command = ['adb', 'connect', f'{ip_address}:{port}']
            self.execute_adb_command(connect_command)
            print(f"已连接到设备: {ip_address}:{port}")
            
            # 验证连接
            devices_command = ['adb', 'devices']
            result = self.execute_adb_command(devices_command)
            if f"{ip_address}:{port}" in result.stdout:
                print("无线连接设置成功！")
                # 注册新设备
                self.connection_manager.register_device(f"{ip_address}:{port}", ip_address, port)
                return True
            else:
                print("无线连接验证失败")
                return False
                
        except Exception as e:
            print(f"设置无线连接失败: {str(e)}")
            return False

    def disconnect_wireless(self, ip_address, port=5555):
        """断开无线连接"""
        try:
            subprocess.run(['adb', 'disconnect', f'{ip_address}:{port}'], check=True)
            print(f"已断开与设备 {ip_address}:{port} 的连接")
            return True
        except subprocess.CalledProcessError as e:
            print(f"断开连接失败: {str(e)}")
            return False

    def switch_to_wireless(self, device_id):
        """切换到无线连接模式"""
        try:
            # 获取当前连接的设备ID
            current_device = device_id
            
            # 设置无线连接
            if self.setup_wireless_connection(current_device):
                # 获取新的无线连接设备ID
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, check=True)
                wireless_device = None
                for line in result.stdout.split('\n'):
                    if 'tcpip' in line:
                        wireless_device = line.split()[0]
                        break
                
                if wireless_device:
                    print(f"已切换到无线连接模式，新设备ID: {wireless_device}")
                    self.device_id = wireless_device
                    return True
                else:
                    print("未找到无线连接的设备")
                    return False
            else:
                print("切换到无线连接模式失败")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"切换无线连接失败: {str(e)}")
            return False

    # 新增应用管理相关方法
    def start_douyin_app(self, device_id):
        """启动抖音极速版应用"""
        try:
            print("正在启动抖音极速版...")
            
            # 首先确保屏幕处于唤醒状态
            if not self.ensure_screen_on(device_id):
                print("无法唤醒屏幕，可能会影响启动应用")
                # 继续尝试启动，但可能会失败
            
            # 抖音极速版的包名和Activity名
            package_name = "com.ss.android.ugc.aweme.lite"
            activity_name = "com.ss.android.ugc.aweme.splash.SplashActivity"
            
            # 先尝试关闭可能在后台运行的应用实例
            force_stop_command = f"adb -s {device_id} shell am force-stop {package_name}"
            subprocess.run(force_stop_command, shell=True, capture_output=True, text=True)
            print("已关闭可能存在的抖音极速版后台进程")
            
            # 通过am start命令启动应用
            command = f"adb -s {device_id} shell am start -n {package_name}/{activity_name}"
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            
            if "Error" in result.stdout:
                print(f"启动抖音极速版出错: {result.stdout.strip()}")
                return False
            
            # 等待应用加载完成
            random_wait = random.randint(8, 15)
            print(f"等待应用加载 {random_wait} 秒...")
            self.delay(random_wait)
            
            # 验证应用是否成功启动并处于前台
            window_command = f"adb -s {device_id} shell \"dumpsys window | grep mCurrentFocus\""
            window_result = subprocess.run(window_command, shell=True, capture_output=True, text=True)
            if package_name in window_result.stdout:
                print("已确认抖音极速版成功启动并处于前台")
                return True
            else:
                print("未检测到抖音极速版处于前台，启动可能失败")
                return False
            
        except Exception as e:
            print(f"启动抖音极速版失败: {str(e)}")
            return False
    
    def close_douyin_app(self, device_id):
        """关闭抖音极速版应用"""
        try:
            print("正在关闭抖音极速版...")
            # 抖音极速版的包名
            package_name = "com.ss.android.ugc.aweme.lite"
            
            # 通过am force-stop命令关闭应用
            command = f"adb -s {device_id} shell am force-stop {package_name}"
            self.execute_adb_command(command)
            print("抖音极速版已关闭")
            return True
        except Exception as e:
            print(f"关闭抖音极速版失败: {str(e)}")
            return False
    
    def clear_app_data(self, device_id):
        """清理抖音极速版应用数据缓存"""
        try:
            print("正在清理抖音极速版缓存数据...")
            # 抖音极速版的包名
            package_name = "com.ss.android.ugc.aweme.lite"
            
            # 清理应用缓存
            command = f"adb -s {device_id} shell pm clear {package_name}"
            self.execute_adb_command(command)
            print("抖音极速版缓存数据已清理")
            return True
        except Exception as e:
            print(f"清理抖音极速版缓存失败: {str(e)}")
            return False
    
    def clean_device_memory(self, device_id):
        """清理设备内存"""
        try:
            print("正在清理设备内存...")
            
            # 获取前台进程并关闭非系统应用
            command = f"adb -s {device_id} shell ps"
            result = self.execute_adb_command(command)
            
            # 执行系统内存优化命令
            self.execute_adb_command(f"adb -s {device_id} shell am broadcast -a android.intent.action.CLEAR_RELINQUISHED_MEMORY")
            
            # 清理后台进程
            self.execute_adb_command(f"adb -s {device_id} shell am kill-all")
            
            # 执行垃圾回收
            self.execute_adb_command(f"adb -s {device_id} shell am broadcast -a android.intent.action.SYSTEM_TRIM_MEMORY")
            
            print("设备内存清理完成")
            return True
        except Exception as e:
            print(f"清理设备内存失败: {str(e)}")
            return False
    
    def is_douyin_running(self, device_id):
        """检查抖音极速版是否在运行且处于前台"""
        try:
            package_name = "com.ss.android.ugc.aweme.lite"
            
            # 方法1：检查进程是否存在
            ps_command = f"adb -s {device_id} shell \"ps | grep {package_name}\""
            ps_result = subprocess.run(ps_command, shell=True, capture_output=True, text=True)
            process_exists = package_name in ps_result.stdout
            
            # 方法2：检查是否在前台运行
            window_command = f"adb -s {device_id} shell \"dumpsys window | grep mCurrentFocus\""
            window_result = subprocess.run(window_command, shell=True, capture_output=True, text=True)
            is_foreground = package_name in window_result.stdout
            
            if process_exists and is_foreground:
                print(f"检测到抖音极速版正在运行且在前台")
                return True
            elif process_exists:
                print(f"检测到抖音极速版进程存在，但不在前台，需要重新启动")
                return False
            else:
                print(f"抖音极速版未在运行")
                return False
        except Exception as e:
            print(f"检查抖音极速版运行状态时出错: {str(e)}")
            return False

    def execute_command(self, command, shell=False):
        """
        执行ADB命令
        """
        self.throttle_command()
        
        try:
            if shell:
                result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, shell=True, timeout=self.adb_command_timeout)
            else:
                result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=self.adb_command_timeout)
            
            return result
        except subprocess.TimeoutExpired:
            print(f"警告: 命令执行超时: {command}")
            return None
        except Exception as e:
            print(f"执行命令时出错: {e}")
            return None

    def cleanup_screenshots(self, path='pic'):
        """清理过多的截图文件，只保留最近的一部分"""
        current_time = time.time()
        # 检查是否到了清理时间
        if current_time - self.last_cleanup_time < self.cleanup_interval:
            return
            
        self.last_cleanup_time = current_time
        
        if path == 'pic':
            path = os.path.dirname(__file__) + '/pic'
        else:
            path = os.path.dirname(__file__) + '/target_pic'
            
        if not os.path.exists(path):
            return
            
        try:
            # 获取所有png文件
            screenshot_files = [f for f in os.listdir(path) if f.endswith('.png') and f != 'screenshot.png']
            
            # 如果文件数量超过阈值，则删除旧文件
            if len(screenshot_files) > self.max_screenshots:
                # 按修改时间排序
                screenshot_files.sort(key=lambda f: os.path.getmtime(os.path.join(path, f)))
                
                # 计算需要删除的文件数量
                files_to_delete = len(screenshot_files) - self.max_screenshots
                
                # 删除旧文件
                for i in range(files_to_delete):
                    try:
                        os.remove(os.path.join(path, screenshot_files[i]))
                    except Exception as e:
                        print(f"删除旧截图文件失败: {str(e)}")
                        
                print(f"已清理 {files_to_delete} 个旧截图文件")
        except Exception as e:
            print(f"清理截图文件时出错: {str(e)}")

    def is_screen_on(self, device_id):
        """检查屏幕是否处于点亮状态"""
        try:
            # 针对Android 7.0及以上系统
            cmd = f"adb -s {device_id} shell \"dumpsys power | grep 'Display Power: state='\""
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if "Display Power: state=ON" in result.stdout:
                print("检测到屏幕处于点亮状态")
                return True
            
            # 如果上面的命令没有返回结果，尝试其他方式
            cmd = f"adb -s {device_id} shell \"dumpsys power | grep 'mWakefulness='\""
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if "mWakefulness=Awake" in result.stdout:
                print("检测到屏幕处于唤醒状态")
                return True
            else:
                print("检测到屏幕处于熄灭状态")
                return False
        except Exception as e:
            print(f"检查屏幕状态时出错: {str(e)}")
            # 默认返回True以避免不必要的唤醒操作
            return True
        
    def wake_up_screen(self, device_id):
        """唤醒手机屏幕"""
        try:
            print("正在尝试唤醒屏幕...")
            
            # 发送电源键事件唤醒屏幕
            power_cmd = f"adb -s {device_id} shell input keyevent KEYCODE_POWER"
            subprocess.run(power_cmd, shell=True)
            
            # 等待短暂时间
            time.sleep(2)
            
            # 检查屏幕是否已唤醒
            if self.is_screen_on(device_id):
                print("屏幕已成功唤醒")
                return True
            else:
                # 如果一次不成功，再尝试一次
                print("首次唤醒尝试失败，再次尝试...")
                subprocess.run(power_cmd, shell=True)
                time.sleep(2)
                
                if self.is_screen_on(device_id):
                    print("屏幕已在第二次尝试后唤醒")
                    return True
                else:
                    print("唤醒屏幕失败，可能需要手动操作")
                    return False
        except Exception as e:
            print(f"唤醒屏幕时出错: {str(e)}")
            return False

    def ensure_screen_on(self, device_id):
        """确保屏幕处于唤醒状态，如果不是则唤醒它"""
        if not self.is_screen_on(device_id):
            return self.wake_up_screen(device_id)
        return True

    def turn_off_screen(self, device_id):
        """关闭屏幕（息屏）"""
        try:
            # 首先检查屏幕是否已经关闭
            if not self.is_screen_on(device_id):
                print("屏幕已经处于息屏状态")
                return True
            
            print("正在尝试息屏...")
            
            # 使用电源键关闭屏幕
            power_cmd = f"adb -s {device_id} shell input keyevent KEYCODE_POWER"
            subprocess.run(power_cmd, shell=True)
            
            # 等待短暂时间
            time.sleep(2)
            
            # 验证屏幕是否已关闭
            if not self.is_screen_on(device_id):
                print("屏幕已成功关闭")
                return True
            else:
                print("首次息屏尝试失败，再次尝试...")
                # 再次尝试
                subprocess.run(power_cmd, shell=True)
                time.sleep(2)
                
                if not self.is_screen_on(device_id):
                    print("屏幕已在第二次尝试后成功关闭")
                    return True
                else:
                    print("息屏失败，可能需要手动操作")
                    return False
        except Exception as e:
            print(f"息屏时出错: {str(e)}")
            return False


