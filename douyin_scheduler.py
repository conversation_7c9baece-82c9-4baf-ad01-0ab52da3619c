#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音福袋挂机程序调度器
用于智能调度各种模式和功能，实现更高级的自动化
"""

import os
import sys
import time
import random
from datetime import datetime, timedelta
import threading

# 导入自己的模块
from douyin_fudai import fudai_analyse
from douyin_guaji import fudai_guaji
from Underlying_Operations import underlying_operations

class AppScheduler:
    """抖音应用智能调度器"""
    
    def __init__(self):
        """初始化调度器"""
        print("调度器初始化开始...")
        
        # 🔧 修复：优化对象初始化顺序，避免重复创建
        # 先初始化基础操作对象
        self.operation = underlying_operations()
        print("操作对象初始化完成")
        
        # 初始化福袋分析对象
        self.fudai = fudai_analyse()
        print("福袋处理对象初始化完成")
        
        # 初始化挂机对象，传入已创建的福袋分析对象
        self.guaji = fudai_guaji(external_analyser=self.fudai)
        print("挂机对象初始化完成")
        
        # 初始化线程停止标志
        self._stop_flag = threading.Event()
        
        # 初始化监控线程和福袋状态
        self.monitoring_thread = None
        self.fudai_opened = False
        
        # 设置默认参数
        self.y_pianyi = 0  # Y轴偏移
        self.current_session_time = 0  # 当前会话运行时间
        
        print("调度器初始化完成")
    
    def get_current_hour(self):
        """获取当前小时"""
        return datetime.now().hour
    
    def should_start_app(self):
        """判断是否应该启动应用"""
        current_hour = self.get_current_hour()
        # 早上7点到晚上0点之间可以运行
        return 7 <= current_hour < 24
    
    def should_stop_app(self, start_time):
        """判断是否应该关闭应用"""
        current_hour = self.get_current_hour()
        elapsed_time = datetime.now() - start_time
        
        # 福袋已经开奖
        if self.fudai_opened:
            print("检测到福袋已开奖，准备关闭应用")
            return True
            
        # 凌晨0点到1点之间应该关闭
        if 0 <= current_hour < 1:
            return True
            
        # 应用运行时间超过预定时间，准备休息
        if elapsed_time.total_seconds() > self.current_session_time:
            # 白天(7-23点)可以短暂休息
            if 7 <= current_hour < 23:
                return True
                
        return False
    
    def monitor_fudai_status(self):
        """监控福袋状态的线程"""
        print("开始监控福袋状态")
        self.fudai_opened = False
        
        check_interval = 60  # 每分钟检查一次
        
        while not self._stop_flag.is_set():
            try:
                # 获取最新日志文件
                log_files = [f for f in os.listdir("logs") if f.endswith('.log')]
                if not log_files:
                    time.sleep(check_interval)
                    continue
                    
                # 按修改时间排序，获取最新的日志文件
                latest_log = max([os.path.join("logs", f) for f in log_files], 
                                 key=os.path.getmtime)
                
                # 检查日志中的关键字
                with open(latest_log, 'r', encoding='utf-8', errors='ignore') as f:
                    # 读取文件内容
                    lines = f.readlines()
                    # 只检查最后100行
                    last_lines = lines[-100:] if len(lines) > 100 else lines
                    
                    # 检查是否有中奖、开奖等关键字
                    for line in reversed(last_lines):  # 从最新的日志开始检查
                        if any(keyword in line for keyword in ["中奖了", "开奖成功", "抽奖结束", "成功领取奖品"]):
                            print("在日志中检测到开奖相关信息")
                            self.fudai_opened = True
                            break
            
            except Exception as e:
                print(f"监控福袋状态时出错: {str(e)}")
            
            # 等待一段时间再检查
            time.sleep(check_interval)
        
        print("停止监控福袋状态")
    
    def start_monitoring(self):
        """启动福袋状态监控"""
        self._stop_flag.clear()
        self.monitoring_thread = threading.Thread(target=self.monitor_fudai_status)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
    
    def stop_monitoring(self):
        """停止福袋状态监控"""
        self._stop_flag.set()
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
    
    def check_and_start_douyin(self, device_id):
        """
        检查抖音极速版是否在运行并处于前台，不在则启动
        """
        print("检查抖音极速版是否已启动...")
        if hasattr(self.operation, 'is_douyin_running'):
            is_running = self.operation.is_douyin_running(device_id)
            
            if not is_running:
                print("准备启动抖音极速版...")
                if hasattr(self.operation, 'start_douyin_app'):
                    app_started = self.operation.start_douyin_app(device_id)
                    print(f"应用启动结果: {app_started}")
                    return app_started
                else:
                    print("操作对象没有start_douyin_app方法")
                    return False
            else:
                print("抖音极速版已在前台运行，无需启动")
                return True
        else:
            print("操作对象没有is_douyin_running方法")
            # 尝试直接启动应用
            if hasattr(self.operation, 'start_douyin_app'):
                app_started = self.operation.start_douyin_app(device_id)
                print(f"应用启动结果: {app_started}")
                return app_started
            else:
                print("操作对象没有start_douyin_app方法")
                return False
    

    def run_schedule(self):
        """运行完整的调度器"""
        print("抖音极速版自动调度器已启动")
        
        # 获取设备ID
        device_id = self.operation.select_device()
        
        if not device_id:
            print("未检测到设备，调度器退出")
            return
            
        print(f"已连接到设备: {device_id}")
        
        # 主循环
        while True:
            try:
                current_time = datetime.now()
                current_hour = self.get_current_hour()
                
                # 确定休息时间
                if 0 <= current_hour < 7:
                    # 凌晨休息到早上7点
                    next_run = datetime(current_time.year, current_time.month, current_time.day, 7, 0, 0)
                    if next_run < current_time:  # 如果已经过了今天的7点，则设为明天7点
                        next_run += timedelta(days=1)
                    rest_time = (next_run - current_time).total_seconds()
                    print(f"凌晨时段，将休息至早上7点")
                    
                    # 添加息屏操作，确保在凌晨时段息屏
                    print("凌晨时段，关闭屏幕（息屏）")
                    if hasattr(self.operation, 'turn_off_screen'):
                        self.operation.turn_off_screen(device_id)
                        
                    # 直接休眠到指定时间，不再执行其他操作
                    print(f"休眠{rest_time/3600:.1f}小时后在早上7点继续运行")
                    time.sleep(rest_time)
                    continue
                else:
                    # 检查是否应该启动应用
                    if self.should_start_app():
                        # 生成本次会话的运行时间（4-7小时）
                        self.current_session_time = random.randint(4 * 3600, 7 * 3600)
                        hours = self.current_session_time // 3600
                        minutes = (self.current_session_time % 3600) // 60
                        print(f"计划本次会话运行 {hours} 小时 {minutes} 分钟")
                        
                        # 启动福袋状态监控
                        self.start_monitoring()
                        
                        # 确保屏幕处于唤醒状态
                        print("准备启动应用前检查屏幕状态")
                        if hasattr(self.operation, 'ensure_screen_on'):
                            if not self.operation.ensure_screen_on(device_id):
                                print("尝试唤醒屏幕失败，可能会影响应用启动")
                        
                        # 检查应用是否在运行，不在则启动
                        app_running = self.check_and_start_douyin(device_id)
                        if not app_running:
                            print("无法启动抖音极速版，等待10分钟后重试")
                            time.sleep(600)  # 等待10分钟后重试
                            continue
                        
                        # 记录应用启动时间
                        start_time = datetime.now()
                        print(f"应用启动时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                        
                        # 运行挂机程序
                        print("开始执行福袋挂机任务")
                        process = self.guaji.guaji_with_timeout(self.y_pianyi, self.current_session_time)
                        
                        # 检查是否应该停止应用
                        check_interval = 60  # 每分钟检查一次
                        while not self.should_stop_app(start_time):
                            time.sleep(check_interval)
                        
                        # 在挂机程序结束后关闭应用
                        print("时间到，停止挂机程序")
                        self.guaji.stop_guaji()  # 发送停止信号
                        
                        # 等待挂机程序完全退出
                        wait_time = 0
                        max_wait_time = 30  # 最多等待30秒
                        while wait_time < max_wait_time:
                            if hasattr(self.guaji.analyser, 'is_processing') and self.guaji.analyser.is_processing:
                                print(f"等待挂机程序完全退出，已等待{wait_time}秒...")
                                time.sleep(1)
                                wait_time += 1
                            else:
                                print("挂机程序已完全退出")
                                break
                        
                        # 如果等待超时，记录警告
                        if wait_time >= max_wait_time:
                            print(f"等待挂机程序退出超时({max_wait_time}秒)，继续执行清理操作")
                        
                        # 停止监控线程
                        self.stop_monitoring()
                        
                        # 确保应用被关闭
                        print("关闭抖音极速版并清理内存")
                        if hasattr(self.operation, 'is_douyin_running'):
                            if self.operation.is_douyin_running(device_id):
                                if hasattr(self.operation, 'close_douyin_app'):
                                    self.operation.close_douyin_app(device_id)
                                if hasattr(self.operation, 'clean_device_memory'):
                                    self.operation.clean_device_memory(device_id)
                                
                                # 添加息屏操作，模拟真人使用后锁屏
                                print("程序休眠前关闭屏幕（息屏）")
                                if hasattr(self.operation, 'turn_off_screen'):
                                    self.operation.turn_off_screen(device_id)
                        
                        # 确定休息时间
                        if 0 <= current_hour < 7:
                            # 凌晨休息到早上7点
                            next_run = datetime(current_time.year, current_time.month, current_time.day, 7, 0, 0)
                            if next_run < current_time:  # 如果已经过了今天的7点，则设为明天7点
                                next_run += timedelta(days=1)
                            rest_time = (next_run - current_time).total_seconds()
                            print(f"凌晨时段，将休息至早上7点")
                            
                            # 添加息屏操作，确保在凌晨时段息屏
                            print("凌晨时段，关闭屏幕（息屏）")
                            if hasattr(self.operation, 'turn_off_screen'):
                                self.operation.turn_off_screen(device_id)
                                
                            # 直接休眠到指定时间，不再执行其他操作
                            print(f"程序将休眠 {rest_time//3600:.1f} 小时后在早上7点重新启动")
                            time.sleep(rest_time)
                            # 跳过后续的休息时间设置，直接进入下一个循环
                            continue
                        else:
                            # 白天随机休息1-3小时
                            rest_time = random.randint(3600, 3 * 3600)
                            hours = rest_time // 3600
                            minutes = (rest_time % 3600) // 60
                            print(f"将休息 {hours} 小时 {minutes} 分钟")
                        
                        # 休息一段时间
                        rest_end_time = datetime.now() + timedelta(seconds=rest_time)
                        print(f"预计在 {rest_end_time.strftime('%Y-%m-%d %H:%M:%S')} 后重新启动")
                        time.sleep(rest_time)
                    
            except Exception as e:
                print(f"发生异常: {str(e)}")
                # 停止监控线程
                self.stop_monitoring()
                
                # 确保应用被关闭
                try:
                    if hasattr(self.operation, 'is_douyin_running'):
                        if self.operation.is_douyin_running(device_id):
                            if hasattr(self.operation, 'close_douyin_app'):
                                self.operation.close_douyin_app(device_id)
                            if hasattr(self.operation, 'clean_device_memory'):
                                self.operation.clean_device_memory(device_id)
                            
                            # 异常情况下也执行息屏操作
                            print("异常处理后关闭屏幕（息屏）")
                            if hasattr(self.operation, 'turn_off_screen'):
                                self.operation.turn_off_screen(device_id)
                except Exception as inner_e:
                    print(f"清理过程中发生错误: {str(inner_e)}")
                
                # 等待一段时间后重试
                time.sleep(600)  # 10分钟后重试

if __name__ == "__main__":
    try:
        scheduler = AppScheduler()
        # 使用完整模式运行
        scheduler.run_schedule()
        

    except Exception as e:
        print(f"程序启动失败: {str(e)}") 

