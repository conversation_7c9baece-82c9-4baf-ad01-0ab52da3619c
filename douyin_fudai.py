﻿import os
import sys
import time
import random
import traceback
from PIL import Image
from datetime import datetime
from Underlying_Operations import underlying_operations
import numpy as np
import statistics
import re

# 常量定义
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
PIC_DIR = os.path.join(BASE_DIR, 'pic')
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
SAVE_DIR = os.path.join(PIC_DIR, 'save')

# 创建必要的目录
for directory in [PIC_DIR, LOGS_DIR, SAVE_DIR]:
    if not os.path.exists(directory):
        try:
            os.makedirs(directory)
        except Exception as e:
            print(f"创建目录失败 {directory}: {e}")

class Tee:
    """用于同时输出到控制台和文件的类"""
    def __init__(self, *files):
        self.files = files
        self.original_stdout = sys.stdout

    def write(self, obj):
        for file in self.files:
            file.write(obj)
            file.flush()  # 确保实时写入

    def flush(self):
        for file in self.files:
            file.flush()
            
    def close(self):
        """关闭所有文件"""
        for file in self.files:
            if file != self.original_stdout and not file.closed:
                file.close()
        # 恢复原始stdout
        sys.stdout = self.original_stdout


class fudai_analyse:
    """福袋抽奖相关操作类"""

    def __init__(self):
        """
        初始化福袋分析对象
        """
        # 初始化操作对象
        self.operation = underlying_operations()

        # 初始化停止事件
        self._stop_event = None
        
        # 添加处理状态标志
        self.is_processing = False
        
        # 设置日志输出
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 获取当前时间作为日志文件名
        log_time = time.strftime('%Y-%m-%d-%H-%M', time.localtime())
        log_file = os.path.join(log_dir, f'{log_time}.log')
        
        # 创建日志文件并重定向stdout和stderr
        sys.stdout = sys.stderr = Tee(sys.stdout, open(log_file, 'a', encoding='utf-8'))
        
        # 初始化设备ID
        self.device_id = ""
        
        # 初始化计数器和状态变量
        self.reset_all_counters_and_params()
        
        # 初始化y轴偏移值
        self.y_pianyi = 0
        
        # 初始化分辨率比例
        self.resolution_ratio_x = 1080
        self.resolution_ratio_y = 2280
        
        # 初始化上次找到福袋的时间
        self.last_find_fudai_time = 0.0
        
        # 初始化上次刷新直播列表的时间
        self.last_refresh_zhibo_list_time = 0.0
        
        # 添加累计无理想福袋直播间计数器和阈值，作为类变量
        self.continuous_no_fudai_zhibo = 0
        self.max_no_fudai_zhibo = random.randint(6, 8)
        print(f"初始设定累计{self.max_no_fudai_zhibo}个直播间无福袋或无理想福袋后切换到刷视频模式")

    def set_stop_event(self, stop_event):
        """设置外部停止事件"""
        self._stop_event = stop_event

    def should_stop(self):
        """检查是否应该停止"""
        return self._stop_event is not None and self._stop_event.is_set()

    def __del__(self):
        """析构函数，确保资源正确释放"""
        try:
            # 恢复原始stdout并关闭日志文件
            if hasattr(self, 'tee') and self.tee:
                self.tee.close()
        except Exception as e:
            print(f"关闭日志文件时出错: {e}")

    def deal_robot_pic_change_color(self):
        """处理人机验证的图片，转为黑白图"""
        try:
            self.cut_pic(143, 884, 936, 1380, 'save', 'robot_verification')
            
            # 使用全局常量路径
            pic = os.path.join(SAVE_DIR, 'robot_verification.png')
            
            # 检查文件是否存在
            if not os.path.exists(pic):
                print(f"人机验证图片不存在: {pic}")
                return False
                
            img = Image.open(pic)
            img = img.convert('RGB')
            width, height = img.size
            
            for x in range(5, width - 40):
                for y in range(20, height - 30):
                    current_color = img.getpixel((x, y))
                    if current_color[0] > 240 and current_color[1] > 240 and current_color[2] > 240:
                        img.putpixel((x, y), (255, 255, 255))  # 白色
                    elif current_color[0] < 35 and current_color[1] < 20 and current_color[2] < 20:
                        img.putpixel((x, y), (0, 0, 0))  # 黑色
                    else:
                        img.putpixel((x, y), (128, 128, 128))  # 灰色
                        
            save_pic = os.path.join(SAVE_DIR, 'robot_verification_new.png')
            img.save(save_pic)
            return True
        except Exception as e:
            print(f"处理人机验证图片失败: {e}")
            return False

    def check_robot_pic_distance(self):
        """处理人机验证的图片"""
        self.cut_pic(143, 884, 936, 1380, '', 'robot_verification')
        path = os.path.dirname(__file__) + '/pic'
        pic = path + '/robot_verification.png'
        img = Image.open(pic)
        img = img.convert('RGB')
        width, height = img.size
        printed_first_result = False  # 用于记录第一个结果是否已经输出过
        printed_second_result = False  # 用于记录第二个结果是否已经输出过
        for y in range(20, height - 30):
            for x in range(5, width - 40):
                current_color = img.getpixel((x, y))
                if current_color[0] > 240 and current_color[1] > 240 and current_color[2] > 240:
                    if not printed_first_result:  # 确保只输出一次第一个结果
                        print(x, y)
                        printed_first_result = True
                    break
            if printed_first_result:  # 如果已经输出过第一个结果，则退出外层循环
                break
        for x1 in range(x, width - 40):
            current_color = img.getpixel((x1, y))
            if current_color[0] < 50 and current_color[1] < 55 and current_color[2] < 85 and current_color[0] + \
                    current_color[1] + current_color[2] < 150:
                if not printed_second_result:  # 确保只输出一次第二个结果
                    print(x1, y)
                    printed_second_result = True
                print("需要滑动的距离为{}".format(x1 - x))
                return x1 - x

    def deal_robot_pic(self):
        """处理人机验证的图片"""
        self.cut_pic(143, 884, 936, 1380, 'save', 'robot_verification')
        path = os.path.dirname(__file__) + '/pic/save'
        pic = path + '/robot_verification.png'
        img = Image.open(pic)
        img = img.convert('RGB')
        width, height = img.size
        threshold = 90  # 阈值，用于判断颜色偏差是否较大
        for x in range(5, width - 40):
            for y in range(20, height - 30):
                # 获取当前像素点的颜色
                if x > 5 and y > 20 and x < width - 40 and y < height - 30:  # 跳过图片边沿的像素点
                    # 获取当前像素点的颜色
                    current_color = img.getpixel((x, y))
                    # if len(current_color) != 3:
                    #     raise ValueError(f"Invalid color format at ({x}, {y}): {current_color}")
                    #     # 获取周围像素点的颜色
                    num_deviant_neighbors = 0
                    for dx in range(-1, 2):
                        for dy in range(-1, 2):
                            if 0 <= x + dx < width and 0 <= y + dy < height:  # 确保不超出图像边界
                                neighbor_color = img.getpixel((x + dx, y + dy))
                                if isinstance(neighbor_color, tuple) and len(neighbor_color) == 3:  # 检查颜色格式
                                    neighbor_color = tuple(
                                        min(max(int(c), 0), 255) for c in neighbor_color)  # 确保颜色值在0到255之间
                                    channel_diffs = [abs(a - b) for a, b in zip(current_color, neighbor_color)]
                                    # 如果每个通道的偏差都大于30，则将邻居像素点计数为偏差点
                                    if all(diff > 70 for diff in channel_diffs):
                                        num_deviant_neighbors += 1
                                    # color_diff = sum(abs(a - b) for a, b in zip(current_color, neighbor_color))
                                    # if color_diff > threshold:
                                    #     num_deviant_neighbors += 1
                    # 如果偏差大于阈值的邻居数量大于4，则认为是偏差点
                    if num_deviant_neighbors > 3:
                        img.putpixel((x, y), (255, 255, 255))  # 白色
                    else:
                        img.putpixel((x, y), (0, 0, 0))  # 黑色
        save_pic = path + '/robot_verification_new.png'
        img.save(save_pic)

    def check_detail_height(self):
        """判定福袋弹窗的高度，会因为抽奖所需任务不同稍有区别,分别有不要任务、1/2个任务"""
        path = os.path.dirname(__file__) + '/pic'
        pic1_path = path + '/screenshot.png'
        pic = Image.open(pic1_path)
        # pic_new = Image.open(cut_pic_path)
        pic_new = pic.convert('RGBA')
        pix = pic_new.load()
        if 30 <= pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280][0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280][1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280][2] <= 84:
            print('参与抽奖有3个任务')
            return 3
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280 + self.y_pianyi][
            0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280 + self.y_pianyi][
                    1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280 + self.y_pianyi][
                    2] <= 84:
            print('参与抽奖有3个任务')
            return 3
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280 - self.y_pianyi][
            0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280 - self.y_pianyi][
                    1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 945 * self.resolution_ratio_y // 2280 - self.y_pianyi][
                    2] <= 84:
            print('参与抽奖有3个任务')
            return 3
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280][0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280][1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280][2] <= 84:
            print('参与抽奖有2个任务')
            return 2
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280 + self.y_pianyi][
            0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280 + self.y_pianyi][
                    1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280 + self.y_pianyi][
                    2] <= 84:
            print('参与抽奖有2个任务')
            return 2
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280 - self.y_pianyi][
            0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280 - self.y_pianyi][
                    1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1038 * self.resolution_ratio_y // 2280 - self.y_pianyi][
                    2] <= 84:
            print('参与抽奖有2个任务')
            return 2
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280][0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280][1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280][2] <= 84:
            print('参与抽奖有1个任务')
            return 1
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280 + self.y_pianyi][
            0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280 + self.y_pianyi][
                    1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280 + self.y_pianyi][
                    2] <= 84:
            print('参与抽奖有1个任务')
            return 1
        elif 30 <= pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280 - self.y_pianyi][
            0] <= 38 and 34 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280 - self.y_pianyi][
                    1] <= 40 and 78 <= \
                pix[536 * self.resolution_ratio_x // 1080, 1100 * self.resolution_ratio_y // 2280 - self.y_pianyi][
                    2] <= 84:
            print('参与抽奖有1个任务')
            return 1
        elif self.check_have_robot_analyse():  # 如果打开的弹窗是个人机校验
            self.deal_robot_analyse()
        print('参与抽奖不需要任务')
        return 0

    def check_have_fudai(self):
        """判定直播页面福袋的小图标是否存在"""
        path = os.path.dirname(__file__) + '/pic'
        pic1_path = path + '/screenshot.png'
        
        # 只进行一次截图
        self.operation.get_screenshot(self.device_id)
        
        # 检查直播间是否关闭
        if self.check_zhibo_is_closed():
            return False
            
        # 打开并处理图片
        try:
            pic = Image.open(pic1_path)
            pic_new = pic.convert('RGBA')
            pix = pic_new.load()
            
            # 检查福袋图标的特定颜色
            for x in range(31, 410):
                if 194 <= pix[x * self.resolution_ratio_x // 1080, 350 * self.resolution_ratio_y // 2280 + self.y_pianyi][0] <= 200 and \
                   180 <= pix[x * self.resolution_ratio_x // 1080, 350 * self.resolution_ratio_y // 2280 + self.y_pianyi][1] <= 193 and \
                   241 <= pix[x * self.resolution_ratio_x // 1080, 350 * self.resolution_ratio_y // 2280 + self.y_pianyi][2] <= 247:
                    # 找到福袋，更新时间并返回位置
                    self.last_find_fudai_time = time.time()
                    # 随机生成福袋点击位置，x范围为x+5到x+70，y范围为315到380
                    click_x = x + random.randint(5, 70)
                    click_y = random.randint(315, 380)
                    print(f"检测到福袋，原始x坐标: {x}，将在随机位置点击 ({click_x}, {click_y})")
                    return click_x
                    
            # 检查是否需要处理人机验证
            self.deal_robot_analyse()
            
            # 没有找到福袋
            return False
        except Exception as e:
            print(f"检查福袋时出错: {str(e)}")
            return False

    def cut_pic(self, x_top=1, y_top=1, x_bottom=2, y_bottom=2, target='', cut_pic_name='', y_pianyi=0):
        """
        裁剪图片
        
        参数:
        x_top, y_top: 左上角坐标
        x_bottom, y_bottom: 右下角坐标
        target: 目标文件夹，默认为空，使用pic文件夹
        cut_pic_name: 裁剪后的图片名称
        y_pianyi: y轴偏移值，如果未提供则使用类的y_pianyi属性
        """
        # 如果未提供y_pianyi参数，则使用类的y_pianyi属性
        if y_pianyi == 0:
            y_pianyi = self.y_pianyi
            
        # 应用y轴偏移
        y_top_with_offset = y_top + y_pianyi
        y_bottom_with_offset = y_bottom + y_pianyi
        
        # 确保坐标不超出图片范围
        y_top_with_offset = max(0, y_top_with_offset)
        y_bottom_with_offset = max(0, y_bottom_with_offset)
            
        try:
            if target == '':
                path = os.path.dirname(__file__) + '/pic'
            else:
                path = os.path.dirname(__file__) + '/pic/' + target
                
            # 确保目标文件夹存在
            if not os.path.exists(path):
                os.makedirs(path)
                
            pic1_path = os.path.dirname(__file__) + '/pic/screenshot.png'
            
            # 检查源文件是否存在
            if not os.path.exists(pic1_path):
                print(f"源截图文件不存在: {pic1_path}")
                return False
                
            # 计算实际坐标，考虑分辨率
            x_top_actual = x_top * self.resolution_ratio_x // 1080
            y_top_actual = y_top_with_offset * self.resolution_ratio_y // 2280
            x_bottom_actual = x_bottom * self.resolution_ratio_x // 1080
            y_bottom_actual = y_bottom_with_offset * self.resolution_ratio_y // 2280
            
            # 确保坐标有效
            if x_top_actual >= x_bottom_actual or y_top_actual >= y_bottom_actual:
                print(f"无效的裁剪坐标: ({x_top_actual}, {y_top_actual}) - ({x_bottom_actual}, {y_bottom_actual})")
                return False
                
            pic = Image.open(pic1_path)
            pic_width, pic_height = pic.size
            
            # 确保裁剪区域不超出图片范围
            x_bottom_actual = min(x_bottom_actual, pic_width)
            y_bottom_actual = min(y_bottom_actual, pic_height)
            
            # 裁剪图片
            pic_new = pic.crop((x_top_actual, y_top_actual, x_bottom_actual, y_bottom_actual))
            
            # 保存裁剪后的图片
            cut_pic_path = path + '/' + cut_pic_name + '.png'
            pic_new.save(cut_pic_path)
            return True
        except Exception as e:
            print(f"裁剪图片时出错: {str(e)}")
            return False

    def check_have_robot_analyse(self):
        """检查是否存在人机校验"""
        self.cut_pic(130, 790, 680, 870, '', 'zhibo_yanzheng')
        result = self.operation.analyse_pic_word('zhibo_yanzheng', 1)
        if "验证" in result:
            print("存在滑动图片人机校验，需要等待完成验证.")
            return 1
        elif "形状相同" in result:
            print("存在点击图片人机校验，需要等待完成验证.")
            return 2
        self.cut_pic(340, 1415, 700, 1518, '', 'zhibo_yanzheng')
        result = self.operation.analyse_pic_word('zhibo_yanzheng')
        if "开始检测" in result:
            print("存在人脸识别人机校验，需要等待完成验证.")
            return 3
        return False

    def deal_swipe_robot_analyse(self, distance=400):
        """处理滑动图片的人机验证"""
        if distance:
            targetx = (222 + distance) * self.resolution_ratio_x // 1080
        else:
            targetx = 622 * self.resolution_ratio_x // 1080
        self.swipe(222, 1444, targetx, 1444, 300)
        print("滑轨滑动{}距离解锁人机验证".format(distance))
        self.operation.delay(1)

    def deal_robot_analyse(self):
        """处理人机校验，包含各种情况"""
        swipe_times = 0
        while swipe_times < 10:
            robot_result = self.check_have_robot_analyse()
            if robot_result == 1:
                distance = self.check_robot_pic_distance()
                self.deal_swipe_robot_analyse(distance)
                self.operation.delay(10)
                self.operation.get_screenshot(self.device_id)
            elif robot_result == 2:
                print("无法处理图片验证的人机，点击关闭退出验证，等待30分钟")
                self.click(910, 800)  # 点击关闭
                self.operation.delay(1800)
                break
            elif robot_result == 3:
                print("无法处理人脸识别验证的人机，点击返回退出验证，等待30分钟")
                self.operation.click_back(self.device_id)
                self.operation.delay(2)
                self.operation.click_back(self.device_id)
                self.operation.delay(1800)
                break
            else:
                break
            swipe_times += 1
        if swipe_times >= 10:
            print("无法处理图片验证的人机，点击关闭退出验证，等待30分钟")
            self.click(910, 800)  # 点击关闭
            self.operation.delay(1800)

    def swipe(self, left_up_x=0, left_up_y=0, right_down_x=1080, right_down_y=1500, steps=200):
        """调整比例后划动屏幕"""
        left_up_x = left_up_x * self.resolution_ratio_x // 1080
        right_down_x = right_down_x * self.resolution_ratio_x // 1080
        left_up_y = left_up_y * self.resolution_ratio_y // 2280
        right_down_y = right_down_y * self.resolution_ratio_y // 2280
        self.operation.swipe(self.device_id, left_up_x, left_up_y, right_down_x, right_down_y, steps)
        
    def swipe_up_live_room(self, reason=""):
        """
        使用贝塞尔曲线或随机偏移上划切换直播间，更自然的滑动轨迹
        
        参数:
        reason: 上划切换直播间的原因，用于日志输出
        """
        # 随机选择使用贝塞尔曲线还是普通上划
        if random.random() < 0.7:  # 70%概率使用贝塞尔曲线
            # 使用贝塞尔曲线上划，更自然流畅
            duration = random.randint(400, 800)  # 随机持续时间
            control_points = random.randint(1, 3)  # 随机控制点数量
            self.operation.swipe_up_bezier(self.device_id, duration=duration, control_points=control_points)
        else:
            # 使用普通上划但添加随机偏移
            self.operation.swipe_up(self.device_id, start_x_offset=50, start_y_offset=30)
        
        # 输出上划原因
        if reason:
            print(f"{reason}，上划切换直播间")
        else:
            print("上划切换直播间")

    def click(self, x=500, y=500):
        """调整比例后点击坐标位置"""
        x = x * self.resolution_ratio_x // 1080
        y = y * self.resolution_ratio_y // 2280
        self.operation.click(self.device_id, x, y)

    def reflash_zhibo(self):
        """在关注列表，下拉刷新直播间"""
        print("下划刷新直播间列表")
        self.swipe(760, 700, 760, 1500)
        self.operation.delay(5)

    def check_in_follow_list(self):
        """判断是否界面在我的关注的列表页"""
        self.cut_pic(211, 100, 885, 198, '', 'zhibo_follow_list')
        zhibo_list_title = self.operation.analyse_pic_word('zhibo_follow_list')
        
        # 检查是否同时包含"互关"、"关注"和"粉丝"三个关键词
        keywords = ["互关", "关注", "粉丝"]
        detected_keywords = []
        for keyword in keywords:
            if keyword in zhibo_list_title:
                detected_keywords.append(keyword)
        
        print(f"检测到关键词: {', '.join(detected_keywords)}")
        
        # 必须同时存在三个关键词
        if len(detected_keywords) == 3:
            print("当前界面在用户关注列表")
            return True
        return False

    def check_in_zhibo_list(self):
        """检查是否当前在关注的直播列表"""
        self.cut_pic(417, 111, 658, 192, '', 'zhibo_list_title')
        zhibo_list_title = self.operation.analyse_pic_word('zhibo_list_title')
        if "正在直播" in zhibo_list_title:
            print("当前界面已经在直播间列表")
            return True
        return False

    def check_zhibo_is_closed(self, use_existing_screenshot=False):
        """
        检查当前直播间是否关闭
        
        参数:
        use_existing_screenshot: 是否使用已有的截图，True表示使用已有截图，False表示重新截图
        """
        if not use_existing_screenshot:
            self.operation.get_screenshot(self.device_id)
            
        self.cut_pic(394, 181, 695, 279, '', 'zhibo_status')
        zhibo_status = self.operation.analyse_pic_word('zhibo_status')
        if "直播已结束" in zhibo_status or "暂时离开" in zhibo_status or "回放" in zhibo_status:
            print("当前直播间已关闭")
            return True
        
        # 检查是否有"猜你喜欢"的提示，这也表示直播已结束
        if self.check_zhibo_is_closed_guess_whatyoulike(use_existing_screenshot=use_existing_screenshot):
            return True
            
        print("当前直播间正常进行中")
        return False

    def check_zhibo_is_closed_guess_whatyoulike(self, use_existing_screenshot=False):
        """检查是否有猜你喜欢的提示，这表示直播已结束"""
        if not use_existing_screenshot:
            self.operation.get_screenshot(self.device_id)
            
        self.cut_pic(394, 1181, 695, 1279, '', 'zhibo_guess')
        zhibo_guess = self.operation.analyse_pic_word('zhibo_guess')
        if "猜你喜欢" in zhibo_guess:
            print("检测到猜你喜欢提示，直播间已关闭")
            return True
        return False

    def back_to_zhibo_list(self):
        """功能初始化，回到直播间列表"""
        click_back_times = 0
        while click_back_times < 4:
            self.operation.get_screenshot(self.device_id)
            if self.check_in_zhibo_list():
                return False
            elif self.check_in_follow_list():
                self.click(410, 507)
                print("点击打开直播间的列表")
                self.operation.delay(2)
                return False
            self.operation.click_back(self.device_id)
            self.operation.delay(2)
            click_back_times += 1

    def into_zhibo_from_list(self):
        """从直播列表进入直播间"""
        max_attempts = 3  # 最大尝试次数
        for attempt in range(max_attempts):
            self.operation.get_screenshot(self.device_id)
            if self.check_in_zhibo_list():
                current_hour = self.operation.get_current_hour()
                if 2 <= current_hour <= 6:
                    print("凌晨时段，等待10分钟继续检查")
                    self.operation.delay(600)  # 等待10分钟继续检查
                else:
                    self.reflash_zhibo()  # 刷新直播间列表
                    if current_hour > 7:  # 如果当前时间已经早上8点多了，一定有直播间了
                        self.click(282, 588)  # 点击第一个直播间
                        print("点击打开第一个直播间")
                        # 等待加载
                        self.operation.delay(5)
                        # 验证是否成功进入直播间
                        self.operation.get_screenshot(self.device_id)
                        if self.check_in_live_room():
                            print("成功进入直播间")
                            return True
                    elif self.check_zhibo_list_have_zhibo():  # 如果存在直播间
                        self.click(282, 588)  # 点击第一个直播间
                        print("点击打开第一个直播间")
                        # 等待加载
                        self.operation.delay(5)
                        # 验证是否成功进入直播间
                        self.operation.get_screenshot(self.device_id)
                        if self.check_in_live_room():
                            print("成功进入直播间")
                            return True
                    else:  # 如果直播列表是空的，则退出到关注列表
                        self.operation.click_back(self.device_id)
                        self.operation.delay(3)
                        print("直播列表为空，点击退出到关注列表")
            elif self.check_in_live_room():
                print("已经在直播间内")
                return True
            elif self.check_zhibo_have_popup():
                self.click(540, 1620)
                print("点击关闭红包弹窗")
                self.operation.click_back(self.device_id)
                self.operation.delay(3)
                print("点击退出直播间")
            elif self.check_zhibo_is_closed():
                self.operation.click_back(self.device_id)
                self.operation.delay(3)
                print("直播已关闭，点击退出直播间")
            elif self.check_in_follow_list():
                self.click(410, 507)
                print("点击打开直播间的列表")
                self.operation.delay(2)
            else:
                print("当前页面状态未知，等待30秒后重试")
                self.operation.delay(30)
        
        print(f"经过{max_attempts}次尝试，未能成功进入直播间")
        return False

    def check_zhibo_list_have_zhibo(self):
        """检查直播列表是否存在直播的内容"""
        self.operation.get_screenshot(self.device_id)
        path = os.path.dirname(__file__) + '/pic'
        pic1_path = path + '/screenshot.png'
        pic = Image.open(pic1_path)
        # pic_new = Image.open(cut_pic_path)
        pic_new = pic.convert('RGBA')
        pix = pic_new.load()
        if pix[290 * self.resolution_ratio_x // 1080, 490 * self.resolution_ratio_y // 2280][0] == 255 and \
                pix[290 * self.resolution_ratio_x // 1080, 490 * self.resolution_ratio_y // 2280][1] == 255 and \
                pix[290 * self.resolution_ratio_x // 1080, 490 * self.resolution_ratio_y // 2280][2] == 255:
            print('直播间列表为空')
            return False
        print('直播间列表存在直播的内容')
        return True

    def check_zhibo_have_popup(self):
        """判断直播间是否弹出了节假日红包弹窗"""
        self.cut_pic(425, 880, 660, 960, '', 'zhibo_hongbao')
        zhibo_list_title = self.operation.analyse_pic_word('zhibo_hongbao', 1)
        if "最高金额" in zhibo_list_title:
            print("直播间有红包弹窗")
            return True
        return False

    def check_in_homepage(self):
        """判断是否在首页推荐视频界面"""
        # 检查顶部的"推荐"字样
        self.cut_pic(702, 119, 806, 203, '', 'homepage_title')
        homepage_title = self.operation.analyse_pic_word('homepage_title')
        
        # 检查底部导航栏是否包含至少2个导航项
        self.cut_pic(30, 2059, 1037, 2141, '', 'bottom_nav')
        bottom_nav = self.operation.analyse_pic_word('bottom_nav')
        
        # 计算包含的关键词数量
        nav_keywords = ["首页", "朋友", "消息", "我"]
        keywords_found = sum(1 for keyword in nav_keywords if keyword in bottom_nav)
        
        # 检查是否同时满足两个条件：1.包含"推荐" 2.底部导航栏至少包含2个关键词
        if "推荐" in homepage_title and keywords_found >= 2:
            print("当前界面在首页推荐视频界面")
            return True
        return False

    def check_in_personal_center(self):
        """判断是否在个人中心界面"""
        # 检查"编辑主页"文字
        self.cut_pic(772, 604, 1021, 682, '', 'personal_center')
        personal_center = self.operation.analyse_pic_word('personal_center')
        
        # 检查底部导航栏是否包含至少2个导航项
        self.cut_pic(30, 2059, 1037, 2141, '', 'bottom_nav')
        bottom_nav = self.operation.analyse_pic_word('bottom_nav')
        
        # 计算包含的关键词数量
        nav_keywords = ["首页", "朋友", "消息", "我"]
        keywords_found = sum(1 for keyword in nav_keywords if keyword in bottom_nav)
        
        # 检查是否同时满足两个条件：1.包含"编辑主页" 2.底部导航栏至少包含2个关键词
        if "编辑主页" in personal_center and keywords_found >= 2:
            print("当前界面在个人中心")
            return True
        return False

    def check_in_video_page(self):
        """判断是否在视频浏览页面"""
        # 获取计算后的坐标
        top_left = self.operation.calculate_coordinate(920, 90)
        bottom_right = self.operation.calculate_coordinate(1050, 170)
        
        # 检查右上角是否存在搜索按钮
        self.cut_pic(top_left[0], top_left[1], bottom_right[0], bottom_right[1], '', 'video_page_search')
        video_page_result = self.operation.analyse_pic_word('video_page_search')
        
        # 没有特定文字，通过底部导航栏判断
        nav_top_left = self.operation.calculate_coordinate(44, 2060)
        nav_bottom_right = self.operation.calculate_coordinate(174, 2140)
        
        # 检查底部导航栏的"首页"是否存在
        self.cut_pic(nav_top_left[0], nav_top_left[1], nav_bottom_right[0], nav_bottom_right[1], '', 'video_nav')
        video_nav = self.operation.analyse_pic_word('video_nav')
        
        if "首页" in video_nav:
            print("当前界面在视频浏览页面")
            return True
        return False

    def navigate_from_any_page(self, back_count=0, max_back_count=5, restart_attempted=False):
        """
        从任何页面导航到直播间
        
        参数:
        back_count: 当前已尝试回退的次数
        max_back_count: 最大回退尝试次数
        restart_attempted: 是否已经尝试过重启应用
        
        返回:
        True: 成功导航到直播间
        False: 导航失败
        """
        print(f"开始智能导航至直播间...(回退尝试: {back_count}/{max_back_count}, 重启尝试: {'是' if restart_attempted else '否'})")
        
        try:
            self.operation.get_screenshot(self.device_id)
        except Exception as e:
            print(f"获取截图失败: {e}")
            time.sleep(3)
            # 如果截图失败，也计入回退次数
            return self.navigate_from_any_page(back_count + 1, max_back_count, restart_attempted)

        # 检查是否达到最大回退次数
        if back_count >= max_back_count:
            # 如果已经尝试过重启应用但仍然无法识别页面，则停止运行
            if restart_attempted:
                print("已尝试重启应用，但仍然无法识别页面，停止运行")
                self.save_debug_screenshot("failed_after_restart")
                # 关闭应用
                cmd = f"adb -s {self.device_id} shell am force-stop com.ss.android.ugc.aweme.lite"
                os.system(cmd)
                # 返回False表示导航失败且需要停止运行
                return False
            else:
                print(f"已达到最大回退尝试次数({max_back_count})，重启应用")
                self.restart_douyin_app()
                # 重启后重新尝试导航，重置回退计数，标记已尝试重启
                return self.navigate_from_any_page(0, max_back_count, True)
            
        # 第4次尝试时，检查是否是账号下线通知
        if back_count == 3:
            print("第4次尝试，检查是否存在账号下线通知")
            if self.check_account_offline_notice():
                print("检测到账号下线通知，停止运行")
                # 保存截图
                self.save_debug_screenshot("account_offline")
                # 关闭应用
                cmd = f"adb -s {self.device_id} shell am force-stop com.ss.android.ugc.aweme.lite"
                os.system(cmd)
                # 返回False表示导航失败且需要停止运行
                return False
                
        # 第3次尝试时，保存调试截图
        if back_count == 2:
            print("第3次尝试，保存当前页面截图用于调试")
            self.save_debug_screenshot("unknown_page")

        # 判断当前在哪个界面 - 改进检测顺序，确保所有可能的界面都被检测
        if self.check_in_live_room():
            print("当前已在直播间，无需导航")
            return True
        elif self.check_in_zhibo_list():
            print("当前已在直播列表，直接进入直播间")
            self.into_zhibo_from_list()
            # 确认是否成功进入直播间
            self.operation.delay(5)
            self.operation.get_screenshot(self.device_id)
            if self.check_in_live_room():
                print("成功进入直播间")
                return True
            return False
        elif self.check_in_follow_list():
            print("当前在关注列表，点击打开直播间列表")
            self.click(410, 507)
            delay_time = random.randint(3, 6)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            self.operation.get_screenshot(self.device_id)
            
            if self.check_in_zhibo_list():
                print("成功进入直播列表，准备进入直播间")
                self.into_zhibo_from_list()
                # 确认是否成功进入直播间
                self.operation.delay(5)
                self.operation.get_screenshot(self.device_id)
                if self.check_in_live_room():
                    print("成功进入直播间")
                    return True
            return False
        elif self.check_in_personal_center():
            print("当前在个人中心，点击关注导航到直播间")
            # 获取"关注"按钮的坐标
            follow_button = self.operation.calculate_coordinate(425, 635)
            self.click(follow_button[0], follow_button[1])
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            # 重新截图检查是否已进入关注列表
            self.operation.get_screenshot(self.device_id)
            if self.check_in_follow_list():
                print("成功进入关注列表，准备打开直播列表")
                self.click(410, 507)
                delay_time = random.randint(3, 6)
                print(f"等待页面加载 {delay_time} 秒...")
                self.operation.delay(delay_time)
                self.operation.get_screenshot(self.device_id)
                
                if self.check_in_zhibo_list():
                    print("成功进入直播列表，准备进入直播间")
                    self.into_zhibo_from_list()
                    # 确认是否成功进入直播间
                    self.operation.delay(5) 
                    self.operation.get_screenshot(self.device_id)
                    if self.check_in_live_room():
                        print("成功进入直播间")
                        return True
            return False
        elif self.check_in_homepage() or self.check_in_video_page():
            print("当前在首页/视频页面，导航到个人中心")
            # 获取"我"按钮的坐标
            me_button = self.operation.calculate_coordinate(970, 2100)
            self.click(me_button[0], me_button[1])
            # 增加页面切换延迟，随机5-10秒
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            # 重新截图检查是否成功进入个人中心
            self.operation.get_screenshot(self.device_id)
            if self.check_in_personal_center():
                print("成功进入个人中心，继续导航")
                # 获取"关注"按钮的坐标
                follow_button = self.operation.calculate_coordinate(425, 635)
                self.click(follow_button[0], follow_button[1])
                # 增加延迟
                delay_time = random.randint(5, 10)
                print(f"等待页面加载 {delay_time} 秒...")
                self.operation.delay(delay_time)
                
                # 重新截图检查是否已进入关注列表
                self.operation.get_screenshot(self.device_id)
                if self.check_in_follow_list():
                    print("成功进入关注列表，准备打开直播列表")
                    self.click(410, 507)
                    delay_time = random.randint(3, 6)
                    print(f"等待页面加载 {delay_time} 秒...")
                    self.operation.delay(delay_time)
                    self.operation.get_screenshot(self.device_id)
                    
                    if self.check_in_zhibo_list():
                        print("成功进入直播列表，准备进入直播间")
                        self.into_zhibo_from_list()
                        # 确认是否成功进入直播间
                        self.operation.delay(5)
                        self.operation.get_screenshot(self.device_id)
                        if self.check_in_live_room():
                            print("成功进入直播间")
                            return True
            else:
                print("未能成功进入个人中心，重试")
                self.click(me_button[0], me_button[1])
                # 再次增加随机延迟
                delay_time = random.randint(5, 10)
                print(f"等待页面加载 {delay_time} 秒...")
                self.operation.delay(delay_time)
                self.operation.get_screenshot(self.device_id)
        
        # 如果不在任何已知页面或导航失败，尝试回退再判断
        print(f"未能识别当前页面或导航失败，尝试回退 (第{back_count + 1}次)")
        self.operation.click_back(self.device_id)
        # 增加延迟
        delay_time = random.randint(5, 10) 
        print(f"回退后等待页面加载 {delay_time} 秒...")
        self.operation.delay(delay_time)
        
        # 递归调用并增加回退计数
        return self.navigate_from_any_page(back_count + 1, max_back_count, restart_attempted)

    def check_no_fudai_time(self):
        """无福袋等待时间检查"""
        if 3 <= self.operation.get_current_hour() <= 6:  # 如果是凌晨3-6点
            self.last_find_fudai_time = 0.00
        elif self.last_find_fudai_time == 0.00 or self.last_find_fudai_time == 0:  # 如果过了不挂机时间，把当前时间赋值给上次找到福袋的时间
            self.last_find_fudai_time = time.time()
        if self.last_find_fudai_time > 0:
            current_time = time.time()
            wait_time = current_time - self.last_find_fudai_time
            wait_time = round(wait_time, 1)
            if wait_time > 18000:
                wait_time = 0
            if wait_time > 1:
                print("距离上一次识别到福袋已经过去{}秒".format(wait_time))
            return wait_time
        return 0

    def get_fudai_contain(self, renwu=2):
        """获取福袋的内容和倒计时"""
        if renwu == 2:  # 如果是2个任务的
            self.cut_pic(336, 1262, 1031, 1471, '', 'fudai_content', self.y_pianyi)  # 福袋内容详情
            self.cut_pic(399, 1152, 562, 1228, '', 'fudai_countdown')  # 完整福袋详情倒计时
        elif renwu == 1:  # 如果是1个任务的
            self.cut_pic(336, 1331, 1031, 1535, '', 'fudai_content', self.y_pianyi)  # 福袋内容详情
            self.cut_pic(399, 1209, 562, 1291, '', 'fudai_countdown')  # 完整福袋详情倒计时
        elif renwu == 3:  # 如果是3个任务的
            self.cut_pic(336, 1182, 1031, 1388, '', 'fudai_content', self.y_pianyi)  # 福袋内容详情
            self.cut_pic(399, 1052, 562, 1136, '', 'fudai_countdown')  # 完整福袋详情倒计时
        else:  # 如果是没有个任务的或者特殊的
            self.cut_pic(336, 1560, 1031, 1766, '', 'fudai_content', self.y_pianyi)  # 福袋内容详情
            self.cut_pic(399, 1444, 562, 1528, '', 'fudai_countdown')  # 完整福袋详情倒计时
        fudai_content_text = self.operation.analyse_pic_word('fudai_content', 1)
        print("福袋内容：{}".format(fudai_content_text))
        time_text = self.operation.analyse_pic_word('fudai_countdown', 2)
        print("倒计时时间：{}".format(time_text))
        return fudai_content_text, time_text

    def check_contain(self, contains=''):
        """检查福袋内容是否想要"""
        # 如果是凌晨时段，直接返回False，表示不想要
        if self.operation.get_current_hour() < 7:
            print("当前是凌晨时段，不参与任何福袋抽奖")
            return True
            
        # 不想要的福袋内容列表（返回True表示不想要）
        contains_not_want = []
        
        # 想要的福袋内容列表（返回False表示想要）
        contains_want = ["鱼竿", "钓箱", "钓杆", "钓竿", "综合杆"]
        
        # 检查是否包含想要的内容
        for contain in contains_want:
            if contain in contains:
                print(f"福袋内容包含想要的物品：{contain}，将参与抽奖")
                return False
                
        # 检查是否包含不想要的内容
        for contain in contains_not_want:
            if contain in contains:
                print(f"福袋内容包含不想要的物品：{contain}，不参与抽奖")
                return True
                
        # 默认情况：不在想要列表也不在不想要列表，默认不参与抽奖
        print("福袋内容未匹配到特定规则，默认不参与抽奖")
        return True

    def attend_choujiang(self):
        """点击参与抽奖"""
        click_times = 0
        while click_times < 2:
            self.cut_pic(231, 1943, 810, 2044, '', 'attend_button')  # 参与福袋抽奖的文字
            attend_button_text = self.operation.analyse_pic_word('attend_button', 1)
            print("参与抽奖按钮文字内容：{}".format(attend_button_text))
            if "参与成功" in attend_button_text:  # 如果识别到已经参与抽奖
                print("已经参与，等待开奖")
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭福袋详情
                return True
            elif "还需看播" in attend_button_text:  # 如果识别到已经参与抽奖
                print("已经参与，等待看播时间凑齐开奖")
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭福袋详情
                return True
            elif "无法参与" in attend_button_text:  # 如果识别到无法参与抽奖
                print("条件不满足，无法参与抽奖")
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭福袋详情
                self.operation.delay(1)
                # 添加上划直播间功能，使用贝塞尔曲线实现更自然的滑动
                if random.random() < 0.7:  # 70%概率使用贝塞尔曲线
                    self.operation.swipe_up_bezier(self.device_id, duration=random.randint(400, 800), control_points=random.randint(1, 3))
                else:
                    self.operation.swipe_up(self.device_id, start_x_offset=50, start_y_offset=30)
                print("无法参与抽奖，上划切换直播间")
                
                # 增加累计无理想福袋直播间计数
                self.continuous_no_fudai_zhibo += 1
                print(f"累计{self.continuous_no_fudai_zhibo}/{self.max_no_fudai_zhibo}个直播间无福袋或无理想福袋")
                
                # 🔄 累计无理想福袋直播间数已增加，统一处理逻辑会检查是否达到上限
                
                return False
            elif "时长不足" in attend_button_text:  # 如果识别到无法参与抽奖
                print("看播时长不够了，无法参与抽奖")
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭福袋详情
                return False
            elif "评论" in attend_button_text:
                self.random_click_in_button_area(64, 1944, 1012, 2042, "参与抽奖按钮")  # 点击参与抽奖
                return True
            elif "参与抽奖" in attend_button_text:
                self.random_click_in_button_area(64, 1944, 1012, 2042, "参与抽奖按钮")  # 点击参与抽奖
                return True
            elif "加入粉丝团(1钻石)" in attend_button_text:
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭支付弹窗
                self.operation.delay(1)
                # 添加上划直播间功能
                self.swipe(760, 1600, 760, 800, 200)
                print("需要加入粉丝团(1钻石)，上划切换直播间")
                
                # 🔄 记录连续无理想福袋直播间（统一处理，不在此处触发刷视频）
                self.continuous_no_fudai_zhibo += 1
                print(f"📊 累计{self.continuous_no_fudai_zhibo}/{self.max_no_fudai_zhibo}个直播间无理想福袋（粉丝团限制）")
                
                return False
            elif "开始观看" in attend_button_text:
                self.random_click_in_button_area(64, 1944, 1012, 2042, "参与抽奖按钮")  # 点击参与抽奖
                return True
            elif "粉丝团" in attend_button_text:
                self.random_click_in_button_area(64, 1944, 1012, 2042, "参与抽奖按钮")  # 点击加入粉丝团、点亮粉丝团
                self.operation.delay(2)
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭支付弹窗
                self.operation.delay(1)
                # 添加上划直播间功能
                self.swipe(760, 1600, 760, 800, 200)
                print("需要加入粉丝团，上划切换直播间")
                
                # 🔄 记录连续无理想福袋直播间（统一处理，不在此处触发刷视频）
                self.continuous_no_fudai_zhibo += 1
                print(f"📊 累计{self.continuous_no_fudai_zhibo}/{self.max_no_fudai_zhibo}个直播间无理想福袋（观看时长不足）")
                
                click_times += 1
                self.operation.get_screenshot(self.device_id)
            elif "活动已结束" in attend_button_text:
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭福袋详情
                return False
            elif "开通店铺会员" in attend_button_text:
                self.operation.click_back(self.device_id)
                self.operation.delay(1)
                self.random_click_in_button_area(79, 441, 725, 813, "福袋外部")  # 点击福袋外部关闭入会弹窗
                self.operation.delay(1)
                # 添加上划直播间功能
                self.swipe(760, 1600, 760, 800, 200)
                print("需要开通店铺会员，上划切换直播间")
                
                # 🔄 记录连续无理想福袋直播间（统一处理，不在此处触发刷视频）
                self.continuous_no_fudai_zhibo += 1
                print(f"📊 累计{self.continuous_no_fudai_zhibo}/{self.max_no_fudai_zhibo}个直播间无理想福袋（店铺会员限制）")
                
                return False
            else:
                print("参与抽奖按钮文字没匹配上")
                click_times = 2
                return False
        print("参与抽奖多次点击失败")
        return False

    def check_lucky_draw_result(self):
        """判定福袋抽奖的结果"""
        self.operation.get_screenshot(self.device_id)
        self.cut_pic(350, 655, 740, 755, '', 'lucky_draw_result')  # 没有抽中福袋位置
        lucky_draw_result = self.operation.analyse_pic_word('lucky_draw_result')
        if "没有抽中" in lucky_draw_result:
            self.cut_pic(333, 1154, 737, 1247, '', 'no_award_confirm')  # 我知道了按钮的位置
            no_award_confirm = self.operation.analyse_pic_word('no_award_confirm')
            if "我知道了" in no_award_confirm:
                return 1
            elif "领取并使用" in no_award_confirm:
                return 2
            return 3
        elif "抽中福袋" in lucky_draw_result:
            print("恭喜中奖了！")
            y_value = [1438, 1490]
            for y in y_value:
                self.cut_pic(280, y - 30, 660, y + 30, '', 'have_read_user_agreement')  # 已经阅读并同意用户协议
                have_read_user_agreement = self.operation.analyse_pic_word('have_read_user_agreement')
                if "已阅读" in have_read_user_agreement:
                    return y
        return False

    def check_have_reward_notice_confirm(self):
        """判断是否有领奖的二次确认提醒"""
        self.operation.get_screenshot(self.device_id)
        self.cut_pic(370, 1350, 680, 1440, '', 'reward_notice_confirm')  # 提醒领取奖品的弹窗
        reward_notice_confirm = self.operation.analyse_pic_word('reward_notice_confirm', 1)
        if "我知道了" in reward_notice_confirm:
            print("存在奖品领取提醒")
            return True
        return False

    def check_in_order_confirm_page(self):
        """判断是否在中奖下单后的购买成功的页面"""
        self.operation.get_screenshot(self.device_id)
        self.cut_pic(370, 120, 700, 220, '', 'order_confirm')  # 提醒领取奖品的弹窗
        order_confirm = self.operation.analyse_pic_word('order_confirm')
        if "购买成功" in order_confirm:
            print("福袋商品下单成功！")
            return True
        return False

    def get_reward(self, reward_y=0):
        """中奖后领奖然后返回"""
        self.operation.save_reward_pic(self.device_id)
        self.click(243, reward_y)  # 勾选协议
        self.operation.delay(1)
        self.click(540, reward_y - 140)  # 点击领取
        print("勾选协议，点击领取奖品")
        self.operation.delay(5)
        self.click(886, 2170)  # 点击下单
        print("点击下单")
        self.operation.delay(10)
        while self.check_in_order_confirm_page():
            # self.click(80, 180)  # 点击回退按钮
            self.operation.click_back(self.device_id)
            print("点击回退按钮")
            self.operation.delay(4)
        print("下完单返回到直播间")
        self.operation.delay(4)
        if self.check_lucky_draw_result():
            print("领奖弹窗未关闭，点击关闭弹窗")
            self.click(540, reward_y + 180)
            print("点击坐标位置:540 {}关闭领奖弹窗".format((reward_y + 180) * self.resolution_ratio_y // 2280))
            self.operation.delay(2)
            self.operation.save_reward_pic(self.device_id)
            print("保存关闭领奖弹窗后的截图")
            self.operation.click_back(self.device_id)
            self.operation.delay(2)
        if self.check_have_reward_notice_confirm():
            print("提醒领奖弹窗未关闭，点击我知道了，关闭弹窗")
            self.random_click_in_button_area(242, 1156, 841, 1248, "我知道了")  # 点击"领奖弹窗的我知道了"
            self.operation.delay(2)
        self.operation.delay(30)
        print("关闭中奖提醒后等待30S")

    def deal_battery_level(self):
        """针对电量不足的情况做处理"""
        while self.operation.get_ballery_level(self.device_id) < 30:
            print("设备电量较低，退出到直播列表，等待电量恢复后继续挂机")
            self.back_to_zhibo_list()
            self.operation.delay(1800)  # 挂机30分钟

    def check_stop_charging(self):
        """针对vivo设备，判断长时间连接弹出停止充电的弹窗"""
        self.cut_pic(225, 1951, 865, 2051, '', 'charging_stop_notice')
        charging_stop_notice = self.operation.analyse_pic_word('charging_stop_notice')
        if "充电" in charging_stop_notice:
            print("设备弹出停止充电提醒！")
            return True
        return False

    def random_zhibo_like(self, max_time_seconds):
        """
        在直播间内随机点赞
        
        参数:
        max_time_seconds: 最大允许的点赞总时间(秒)，确保不超过福袋等待时间
        
        返回:
        实际用时(秒)
        """
        # 确保时间安全，预留至少5秒用于返回和处理开奖结果
        if max_time_seconds <= 10:
            print(f"等待时间过短({max_time_seconds}秒)，跳过点赞")
            return 0
            
        # 实际可用时间为总时间减去10秒安全边界
        available_time = max_time_seconds - 10
        print(f"开始直播间随机点赞，最大可用时间：{available_time}秒")
        
        # 随机决定点赞次数(20-80次)
        like_times = random.randint(20, 80)
        print(f"计划点赞{like_times}次")
        
        # 计算每次点赞的间隔时间，模拟人手点击节奏
        # 确保总耗时不超过可用时间
        time_per_like = min(0.8, available_time / like_times)
        
        # 直播间点赞区域坐标范围(68,580,867,1419)
        like_x1, like_y1 = self.operation.calculate_coordinate(68, 580)
        like_x2, like_y2 = self.operation.calculate_coordinate(867, 1419)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行点赞
        for i in range(like_times):
            # 在点赞区域内随机选择一个点
            x = random.randint(like_x1, like_x2)
            y = random.randint(like_y1, like_y2)
            
            # 点击
            self.click(x, y)
            
            # 显示进度
            if (i+1) % 10 == 0 or i == 0 or i == like_times-1:
                print(f"已点赞{i+1}/{like_times}次")
            
            # 随机化点赞间隔，模拟人类行为
            # 70%的情况下使用正常间隔，30%的情况下随机加长间隔
            if random.random() < 0.7:
                # 正常间隔 0.2-0.5秒
                interval = random.uniform(0.2, 0.5) 
            else:
                # 随机长间隔 0.6-1.5秒
                interval = random.uniform(0.6, 1.5)
                
            # 确保不超出总时间限制
            elapsed = time.time() - start_time
            remaining = available_time - elapsed
            if remaining <= 0:
                print(f"已达到最大可用时间，提前结束点赞，共完成{i+1}/{like_times}次")
                break
                
            # 实际暂停时间不超过剩余时间
            actual_interval = min(interval, remaining)
            time.sleep(actual_interval)
            
        # 计算实际用时
        total_time = time.time() - start_time
        print(f"直播间点赞完成，共用时{total_time:.2f}秒")
        return total_time

    def fudai_choujiang(self, device_id="", y_pianyi=0, needswitch=False, wait_minutes=15):
        """
        福袋抽奖主函数
        
        参数:
        device_id: 设备ID
        y_pianyi: y轴偏移值
        needswitch: 是否需要切换直播间
        wait_minutes: 福袋等待时间(分钟)
        """
        # 设置处理状态为进行中
        self.set_processing_state(True)
        
        try:
            if device_id != "":
                self.device_id = device_id
            self.y_pianyi = y_pianyi
            
            # 🔧 修复：确保wait_minutes参数在函数开始时就被处理，避免后面重复初始化
            if wait_minutes <= 0:
                wait_minutes = random.randint(15, 30)
                print(f"重置无效的等待时间，本次设定福袋倒计时超过{wait_minutes}分钟时跳过")
            else:
                print(f"本次设定福袋倒计时超过{wait_minutes}分钟时跳过")
            
            # 检查抖音极速版是否在运行，不在则启动
            if not self.operation.is_douyin_running(self.device_id):
                print("抖音极速版未运行，准备启动...")
                start_success = self.operation.start_douyin_app(self.device_id)
                if start_success:
                    print("抖音极速版启动成功，等待应用加载...")
                    time.sleep(random.randint(5, 10))  # 等待5-10秒，让应用完全加载
                else:
                    print("抖音极速版启动失败，请检查应用是否已安装")
                    return False
            
            # 初始化变量
            wait_times = 0
            swipe_times = 0
            # 使用类变量而不是局部变量
            # continuous_no_fudai_zhibo = 0  # 注释掉局部变量定义
            fudai_not_open_times = 0
            successful_draws = 0  # 成功参与抽奖的总次数
            current_zhibo_draws = 0  # 当前直播间抽奖次数
            consecutive_checks = 0  # 连续检测无福袋的次数
            
            # 随机生成连续检测无福袋的最大次数（8-13次之间随机）
            max_consecutive_checks = random.randint(8, 13)
            print(f"本次设定连续检测{max_consecutive_checks}次(约{max_consecutive_checks*30}秒)无福袋后切换直播间")
            
            # 随机生成抽奖总次数（5-8次随机）
            max_draws_before_video = random.randint(5, 8)
            print(f"本次设定总共参与{max_draws_before_video}次福袋抽奖后切换到刷视频模式")
            
            # 随机生成当前直播间最大抽奖次数（3-8次随机）
            max_draws_per_zhibo = random.randint(3, 8)
            print(f"当前直播间最多抽取{max_draws_per_zhibo}次福袋")

            # 重新设置累计无理想福袋直播间的最大数量（6-8之间随机）
            self.max_no_fudai_zhibo = random.randint(6, 8)
            print(f"本次设定累计{self.max_no_fudai_zhibo}个直播间无理想福袋后切换到刷视频模式")

            # 🔧 修复：删除重复的wait_minutes初始化代码
            # wait_minutes = random.randint(15, 30)
            # print(f"本次设定福袋倒计时超过{wait_minutes}分钟时跳过")
            
            # 设置全局超时时间，避免死循环
            global_start_time = time.time()
            global_timeout = 3600 * 6  # 6小时全局超时
            max_swipe_without_result = 30  # 最大无结果滑动次数
            
            # 🔧 修复：设置随机的最大上划次数（6-8个直播间）
            max_swipe_before_video = random.randint(6, 8)
            print(f"本次设定上划{max_swipe_before_video}个直播间后切换到刷视频模式")
            
            # 先检查是否在直播间
            self.operation.get_screenshot(self.device_id)
            if not self.check_in_live_room():
                print("当前不在直播间，开始智能导航至直播间...")
                # 从任何页面智能导航到直播间
                self.navigate_from_any_page()
            else:
                print("当前已在直播间，无需导航")
            
            # 主循环计数
            main_loop_count = 0

            # 🔧 修复：定义needswitch变量，控制是否切换直播间模式
            needswitch = True  # 默认开启切换直播间模式

            # 主循环
            while True:
                # 检查外部停止信号
                if self.should_stop():
                    print("收到外部停止信号，退出福袋抽奖")
                    break

                # 增加主循环计数并打印
                main_loop_count += 1
                print(f"主循环第{main_loop_count}次执行")

                # 检查全局超时
                if time.time() - global_start_time > global_timeout:
                    print(f"已达到全局超时时间{global_timeout/3600:.1f}小时，退出抽奖")
                    break
                    
                # 🎯 统一刷视频模式触发逻辑 - 优先级1：抽奖次数达到上限
                if successful_draws >= max_draws_before_video:
                    print(f"✅ 触发条件1：已成功参与{successful_draws}次抽奖，达到设定上限{max_draws_before_video}次")
                    print("🎬 切换到刷视频模式")
                    try:
                        self.browse_videos_randomly()
                    except Exception as e:
                        print(f"刷视频模式执行失败: {str(e)}")
                        # 即使刷视频失败，也要继续重置状态

                    # 🔄 使用统一的重置函数
                    (successful_draws, current_zhibo_draws, consecutive_checks, wait_times,
                     swipe_times, max_draws_before_video, max_draws_per_zhibo,
                     max_consecutive_checks, max_swipe_before_video, wait_minutes) = self.reset_all_counters_and_params()

                    # 重新导航到直播间
                    self.navigate_from_any_page()
                    continue

                # 🎯 统一刷视频模式触发逻辑 - 优先级2：累计无理想福袋直播间达到上限
                elif self.continuous_no_fudai_zhibo >= self.max_no_fudai_zhibo:
                    print(f"✅ 触发条件2：已累计{self.continuous_no_fudai_zhibo}个直播间无理想福袋，达到设定上限{self.max_no_fudai_zhibo}个")
                    print("🎬 切换到刷视频模式")
                    try:
                        self.browse_videos_randomly()
                    except Exception as e:
                        print(f"刷视频模式执行失败: {str(e)}")
                        # 即使刷视频失败，也要继续重置状态

                    # 🔄 使用统一的重置函数
                    (successful_draws, current_zhibo_draws, consecutive_checks, wait_times,
                     swipe_times, max_draws_before_video, max_draws_per_zhibo,
                     max_consecutive_checks, max_swipe_before_video, wait_minutes) = self.reset_all_counters_and_params()

                    # 重新导航到直播间
                    self.navigate_from_any_page()
                    continue

                # 🎯 统一刷视频模式触发逻辑 - 优先级3：上划次数达到上限
                elif swipe_times >= max_swipe_before_video:
                    print(f"✅ 触发条件3：已上划{swipe_times}个直播间，达到设定上限{max_swipe_before_video}个")
                    print("🎬 切换到刷视频模式")
                    try:
                        self.browse_videos_randomly()
                    except Exception as e:
                        print(f"刷视频模式执行失败: {str(e)}")
                        # 即使刷视频失败，也要继续重置状态

                    # 🔄 使用统一的重置函数
                    (successful_draws, current_zhibo_draws, consecutive_checks, wait_times,
                     swipe_times, max_draws_before_video, max_draws_per_zhibo,
                     max_consecutive_checks, max_swipe_before_video, wait_minutes) = self.reset_all_counters_and_params()

                    # 重新导航到直播间
                    self.navigate_from_any_page()
                    continue

                # 🔧 修复：优先级4：当前直播间抽奖次数达到上限
                elif current_zhibo_draws >= max_draws_per_zhibo and needswitch:
                    print(f"✅ 触发条件4：当前直播间已抽取{current_zhibo_draws}次福袋，达到设定上限{max_draws_per_zhibo}次")
                    print("🔄 上划切换到下一个直播间")
                    self.swipe(760, 1600, 760, 800, 200)
                    swipe_times += 1

                    # 重置当前直播间相关计数
                    current_zhibo_draws = 0
                    consecutive_checks = 0
                    max_draws_per_zhibo = random.randint(3, 8)
                    print(f"🔄 重置当前直播间计数，新直播间最多抽取{max_draws_per_zhibo}次福袋")

                    self.operation.delay(2)
                    continue
                    
                # 检查是否在直播间
                if not self.check_in_live_room():
                    print("当前不在直播间，尝试导航到直播间")
                    navigation_result = self.navigate_from_any_page()
                    if navigation_result is False:
                        print("导航失败，可能是账号下线或其他严重问题，停止抽奖")
                        break  # 退出主循环
                    continue
                    
                # 截图检查
                try:
                    self.operation.get_screenshot(self.device_id)
                except Exception as e:
                    print(f"截图失败: {str(e)}")
                    self.operation.delay(10)
                    continue
                    
                # 检测是否有人机验证
                robot_result = self.check_have_robot_analyse()
                if robot_result:
                    print("检测到人机验证，等待手动完成")
                    self.operation.delay(60)  # 等待1分钟给用户时间完成验证
                    continue
                    
                # 检测是否有福袋
                x = self.check_have_fudai()

                # 🔧 修复：优先处理检测到福袋的情况
                if x and swipe_times < max_swipe_before_video:
                    # 检测到福袋，重置连续检测计数
                    wait_times = 0
                    consecutive_checks = 0
                    print(f"✅ 检测到福袋，重置连续检测计数")

                    # 延迟抽取福袋，统一延迟抽取福袋时间
                    delay_time = random.randint(1, 295)
                    print(f"检测到福袋，随机等待{delay_time}秒后参与抽奖...")
                    self.operation.delay(delay_time)

                    # 重新截图检查福袋是否还存在
                    try:
                        self.operation.get_screenshot(self.device_id)
                        # 检查是否仍在直播间
                        if not self.check_in_live_room():
                            print("等待后检测到不在直播间，重新导航")
                            self.navigate_from_any_page()
                            continue
                        x_recheck = self.check_have_fudai()
                        if not x_recheck:
                            print("等待后福袋已不存在，继续监测")
                            continue
                        # 更新x值为重新检查的结果
                        x = x_recheck
                    except Exception as e:
                        print(f"重新检查福袋时出错: {str(e)}")
                        continue

                    # 使用随机范围点击福袋
                    click_x = x + random.randint(5, 70)  # x范围为x+5到x+70
                    click_y = random.randint(315, 380)  # y范围为315到380
                    self.click(click_x, click_y)
                    print(f"随机点击福袋位置 ({click_x}, {click_y})")
                    self.operation.delay(3)

                    # 检查福袋内容
                    try:
                        self.operation.get_screenshot(self.device_id)
                        renwu = self.check_detail_height()
                        fudai_content_text, time_text = self.get_fudai_contain(renwu)
                    except Exception as e:
                        print(f"获取福袋内容时出错: {str(e)}")
                        # 点击关闭并继续
                        self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                        self.operation.delay(1)
                        continue

                    # 统一切换与不切换直播间模式的福袋内容判断
                    if self.check_contain(fudai_content_text):  # 如果福袋内容是不想要的
                        self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                        print("点击小福袋位置，关闭福袋详情")
                        self.operation.delay(1)
                        if needswitch:  # 如果是切换直播间模式
                            self.swipe(760, 1600, 760, 800, 200)
                            print("福袋内容不理想，上划切换直播间")
                            swipe_times += 1
                            # 记录连续无理想福袋直播间
                            self.continuous_no_fudai_zhibo += 1
                            print(f"累计{self.continuous_no_fudai_zhibo}/{self.max_no_fudai_zhibo}个直播间无福袋或无理想福袋")

                            # 🔄 累计无理想福袋直播间数已在上面增加，统一处理逻辑会检查是否达到上限

                            current_zhibo_draws = 0  # 重置当前直播间抽奖计数
                            consecutive_checks = 0  # 重置连续检测计数
                            # 重新设置当前直播间最大抽奖次数（3-8次随机）
                            max_draws_per_zhibo = random.randint(3, 8)
                            print(f"重置计数，当前直播间最多抽取{max_draws_per_zhibo}次福袋")
                            self.operation.delay(10)
                            continue
                        else:
                            # 不切换直播间模式，继续监测
                            continue

                    # 福袋内容符合要求，继续处理抽奖逻辑
                    # 检查倒计时
                    try:
                        result = self.operation.check_countdown(time_text)
                        if result:
                            fudai_not_open_times = 0
                            lastsecond, future_timestamp = result
                        else:  # 如果识别到的倒计时内容不太对，则再判定一次
                            self.operation.get_screenshot(self.device_id)
                            renwu = self.check_detail_height()
                            fudai_content_text, time_text = self.get_fudai_contain(renwu)
                            result = self.operation.check_countdown(time_text)
                            if result:
                                fudai_not_open_times = 0
                                lastsecond, future_timestamp = result
                            else:
                                fudai_not_open_times += 1
                                self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                                print("第{}次打开福袋异常，点击小福袋旁边位置，坐标({},{})关闭福袋详情".format(fudai_not_open_times, (
                                        79) * self.resolution_ratio_x // 1080, 441 * self.resolution_ratio_y // 2280))
                                if fudai_not_open_times > 10:
                                    print("超过10次点击福袋无法打开详情，等待30分钟")
                                    self.operation.delay(1800)
                                self.operation.delay(1)
                                continue
                    except Exception as e:
                        print(f"检查倒计时时出错: {str(e)}")
                        self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                        self.operation.delay(1)
                        continue

                    if lastsecond < 15 and needswitch:  # 如果不到15秒了，就不点了
                        self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                        print("点击小福袋位置，关闭福袋详情")
                        self.operation.delay(1)
                        print("抽奖倒计时时间小于15秒，不参与，继续等待下一个福袋")
                        self.operation.delay(5)
                        continue

                    if needswitch and lastsecond >= 60 * wait_minutes:  # 如果需要切换且倒计时时间大于设定的分钟
                        self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                        print("点击小福袋位置，关闭福袋详情")
                        self.operation.delay(1)
                        self.swipe(760, 1600, 760, 800, 200)
                        print("抽奖倒计时时间大于{}分钟，暂不参与，上划切换直播间".format(wait_minutes))
                        swipe_times += 1
                        # 记录连续无理想福袋直播间
                        self.continuous_no_fudai_zhibo += 1
                        current_zhibo_draws = 0  # 重置当前直播间抽奖计数
                        consecutive_checks = 0  # 重置连续检测计数
                        # 重新设置当前直播间最大抽奖次数（3-8次随机）
                        max_draws_per_zhibo = random.randint(3, 8)
                        print(f"重置计数，当前直播间最多抽取{max_draws_per_zhibo}次福袋")
                        self.operation.delay(5)
                        continue

                    # 参与抽奖
                    print("福袋内容符合要求，准备参与抽奖")
                    successful_draws += 1  # 成功参与抽奖计数+1
                    current_zhibo_draws += 1  # 当前直播间抽奖计数+1
                    print(f"当前直播间已抽取{current_zhibo_draws}/{max_draws_per_zhibo}次福袋")

                    # 🔧 修复：直接执行抽奖等待逻辑，不调用不存在的方法
                    # 点击参与抽奖
                    attend_result = self.attend_choujiang()
                    if not attend_result:
                        print("参与抽奖失败，继续监测")
                        continue

                    print(f"成功参与抽奖，总计数：{successful_draws}/{max_draws_before_video}，"
                          f"当前直播间：{current_zhibo_draws}/{max_draws_per_zhibo}")

                    # 随机决定是否在等待开奖期间进行互动（点赞或评论）
                    interaction_type = random.choices(
                        ["none", "like", "comment", "both"],
                        weights=[0.5, 0.2, 0.2, 0.1],
                        k=1
                    )[0]

                    remaining_time = lastsecond

                    # 如果时间充足且需要互动
                    if interaction_type != "none" and remaining_time > 30:
                        print(f"随机决定在等待开奖期间进行互动: {interaction_type}")

                        # 如果需要点赞
                        if interaction_type in ["like", "both"]:
                            # 预留时间用于其他操作
                            like_time = self.random_zhibo_like(remaining_time - 15)
                            remaining_time -= like_time
                            print(f"点赞结束，剩余{remaining_time:.2f}秒")

                            # 如果同时需要评论和点赞，添加一个随机间隔
                            if interaction_type == "both":
                                interval = random.uniform(2, 5)
                                self.operation.delay(interval)
                                remaining_time -= interval

                        # 如果需要评论
                        if interaction_type in ["comment", "both"] and remaining_time > 15:
                            comment_time = self.random_zhibo_comment(remaining_time - 10)
                            remaining_time -= comment_time
                            print(f"评论结束，剩余{remaining_time:.2f}秒")

                        # 等待剩余时间
                        if remaining_time > 0:
                            print(f"互动结束，继续等待开奖，剩余{remaining_time:.2f}秒")
                            self.operation.delay(remaining_time)
                    else:
                        print(f"本次不进行互动，直接等待开奖 {lastsecond} 秒")
                        self.operation.delay(lastsecond+5)  # 比倒计时稍微多等待几秒

                    # 检查开奖结果
                    have_clicked_no_award = False
                    while True:
                        check_result = self.check_lucky_draw_result()
                        if check_result is False:  # 没有弹窗需要处理
                            break
                        elif check_result in (1, 2, 3):  # 未中奖流程
                            self.random_click_in_button_area(242, 1156, 841, 1248, "我知道了")  # 点击"我知道了"
                            print("没有抽中，点击:我知道了,关闭弹窗")
                            self.operation.delay(3)
                            have_clicked_no_award = True
                            if check_result == 2:  # 处理特殊状态（领取并使用）
                                self.random_click_in_button_area(79, 441, 725, 813, "关闭小福袋")  # 点击关闭小福袋
                                self.operation.delay(2)
                        else:  # 中奖流程（返回y坐标）
                            print("检测到中奖结果，执行领奖流程")
                            self.get_reward(check_result)
                            continue

                    if have_clicked_no_award:  # 如果点击了没有中奖
                        if not needswitch:  # 不切换直播间模式
                            self.operation.random_delay(180, 450)
                            continue
                        else:  # 切换直播间模式，但是抽奖次数未达上限，继续在当前直播间抽奖
                            if current_zhibo_draws < max_draws_per_zhibo:
                                self.operation.random_delay(180, 450)
                                continue
                            else:  # 当前直播间抽奖次数已达上限，切换直播间
                                self.swipe(760, 1600, 760, 800, 200)
                                print("当前直播间抽奖次数已达上限，上划切换直播间")
                                swipe_times += 1
                                current_zhibo_draws = 0  # 重置当前直播间抽奖计数
                                consecutive_checks = 0  # 重置连续检测计数
                                # 重新设置当前直播间最大抽奖次数（3-8次随机）
                                max_draws_per_zhibo = random.randint(3, 8)
                                print(f"重置计数，当前直播间最多抽取{max_draws_per_zhibo}次福袋")
                                self.operation.delay(10)
                                continue
                    continue

                # 🔧 修复：优先级5：连续检测多次都没有福袋的处理
                elif consecutive_checks >= max_consecutive_checks and needswitch:
                    print(f"✅ 触发条件5：当前直播间已连续检测{consecutive_checks}次(约{consecutive_checks*30}秒)都没有福袋，准备切换直播间")
                    self.swipe(760, 1600, 760, 800, 200)
                    print("上划切换直播间")
                    swipe_times += 1
                    # 🔄 记录连续无理想福袋直播间（统一处理，不在此处触发刷视频）
                    self.continuous_no_fudai_zhibo += 1
                    print(f"📊 累计{self.continuous_no_fudai_zhibo}/{self.max_no_fudai_zhibo}个直播间无理想福袋（粉丝团限制）")

                    current_zhibo_draws = 0  # 重置当前直播间抽奖计数
                    consecutive_checks = 0  # 重置连续检测计数
                    # 重新设置当前直播间最大抽奖次数（3-8次随机）
                    max_draws_per_zhibo = random.randint(3, 8)
                    print(f"重置计数，当前直播间最多抽取{max_draws_per_zhibo}次福袋")
                    self.operation.delay(2)
                    continue

                # 🔧 修复：没有检测到福袋的情况
                elif not x:
                    wait_times += 1
                    consecutive_checks += 1
                    print(f"当前直播间第{consecutive_checks}次检测无福袋，等待30秒后重新检测")
                    
                    # 使用分段等待，每15秒进行一次状态检查
                    wait_start_time = time.time()
                    
                    # 30秒分成2段，每段15秒
                    wait_segments = 2
                    for i in range(wait_segments):
                        # 检查停止信号
                        if self.should_stop():
                            print("等待期间收到停止信号，退出抽奖")
                            return

                        # 已经等待的时间
                        elapsed_time = time.time() - wait_start_time

                        # 如果已经等待了30秒，跳出循环
                        if elapsed_time >= 30:
                            break

                        # 计算剩余需要等待的时间
                        remaining_time = 30 - elapsed_time
                        segment_time = min(15, remaining_time)  # 每段最多等待15秒

                        # 等待这一段时间
                        if segment_time > 0:
                            self.operation.delay(segment_time)

                        # 最后一段不需要检查
                        if i < wait_segments - 1:
                            # 进行状态检查，包括截图
                            try:
                                self.operation.get_screenshot(self.device_id)
                                # 检查是否仍在直播间
                                if not self.check_in_live_room():
                                    print("检测到不在直播间，准备重新导航")
                                    break
                                if self.check_zhibo_is_closed():
                                    print("检测到直播间已关闭，准备切换直播间")
                                    break
                            except Exception as e:
                                print(f"等待期间检查状态时出错: {str(e)}")
                
                    continue

                # 🔧 修复：其他情况的处理
                else:
                    # 🔧 修复：删除凌晨遗留代码，简化边界情况处理
                    if self.check_zhibo_is_closed():  # 如果直播间已经关闭了
                        print("检测到直播间已关闭，重新导航到直播间")
                        self.back_to_zhibo_list()
                        self.into_zhibo_from_list()
                        swipe_times = 0
                        current_zhibo_draws = 0  # 重置当前直播间抽奖计数
                        consecutive_checks = 0  # 重置连续检测计数
                        # 重新设置当前直播间最大抽奖次数（3-8次随机）
                        max_draws_per_zhibo = random.randint(3, 8)
                        print(f"重置计数，当前直播间最多抽取{max_draws_per_zhibo}次福袋")
                    else:
                        # 默认等待30秒
                        print("其他情况，等待30秒后继续检测")
                        self.operation.delay(30)
                # 🔧 修复：删除所有重复的elif分支，统一由上面的逻辑处理

        finally:
            # 设置处理状态为已完成
            self.set_processing_state(False)

    def reset_all_counters_and_params(self):
        """🔄 统一的重置函数：重置所有计数器和重新生成随机参数"""
        # 重置所有计数器
        successful_draws = 0
        current_zhibo_draws = 0
        consecutive_checks = 0
        wait_times = 0
        swipe_times = 0
        self.continuous_no_fudai_zhibo = 0

        # 🎲 重新生成所有随机参数（确保范围一致）
        max_draws_before_video = random.randint(5, 8)  # 优先级1：抽奖总次数
        max_draws_per_zhibo = random.randint(3, 8)     # 优先级4：当前直播间抽奖次数
        max_consecutive_checks = random.randint(8, 13) # 优先级5：连续检测次数
        self.max_no_fudai_zhibo = random.randint(6, 8) # 优先级2：累计无理想福袋直播间次数
        max_swipe_before_video = random.randint(6, 8)  # 优先级3：所有上划总次数
        wait_minutes = random.randint(15, 30)          # 福袋倒计时等待时间

        print(f"🔄 重置完成，新参数：抽奖总次数{max_draws_before_video}次、单直播间{max_draws_per_zhibo}次、连续检测{max_consecutive_checks}次、无理想福袋{self.max_no_fudai_zhibo}个、上划总次数{max_swipe_before_video}个、等待{wait_minutes}分钟")

        return (successful_draws, current_zhibo_draws, consecutive_checks, wait_times,
                swipe_times, max_draws_before_video, max_draws_per_zhibo,
                max_consecutive_checks, max_swipe_before_video, wait_minutes)

    def navigate_from_any_page_to_homepage(self):
        """从任何页面导航到首页"""
        print("开始智能导航至首页...")
        self.operation.get_screenshot(self.device_id)

        # 判断当前在哪个界面
        if self.check_in_homepage() or self.check_in_video_page():
            print("当前已在首页/视频页面")
            return True
        elif self.check_in_personal_center():
            print("当前在个人中心，返回到首页")
            self.operation.click_back(self.device_id)  # 从个人中心按返回键进入首页
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            return True
        elif self.check_in_zhibo_list():
            print("当前在直播列表，返回到关注列表")
            self.operation.click_back(self.device_id)  # 从直播列表按返回键进入关注列表
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            print("从关注列表返回到个人中心")
            self.operation.click_back(self.device_id)  # 从关注列表按返回键进入个人中心
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            print("从个人中心返回到首页")
            self.operation.click_back(self.device_id)  # 从个人中心按返回键进入首页
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            # 检查是否成功到达首页
            self.operation.get_screenshot(self.device_id)
            if self.check_in_homepage() or self.check_in_video_page():
                print("成功导航至首页")
                return True
            else:
                print("未能成功导航至首页，重试")
                return self.navigate_from_any_page_to_homepage()
        elif self.check_in_follow_list():
            print("当前在关注列表，返回到个人中心")
            self.operation.click_back(self.device_id)  # 从关注列表按返回键进入个人中心
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            print("从个人中心返回到首页")
            self.operation.click_back(self.device_id)  # 从个人中心按返回键进入首页
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            
            # 检查是否成功到达首页
            self.operation.get_screenshot(self.device_id)
            if self.check_in_homepage() or self.check_in_video_page():
                print("成功导航至首页")
                return True
            else:
                print("未能成功导航至首页，重试")
                return self.navigate_from_any_page_to_homepage()
        else:
            # 如果不在任何已知页面，尝试回退再判断
            print("未能识别当前页面，尝试回退")
            self.operation.click_back(self.device_id)
            # 增加延迟
            delay_time = random.randint(5, 10)
            print(f"回退后等待页面加载 {delay_time} 秒...")
            self.operation.delay(delay_time)
            return self.navigate_from_any_page_to_homepage()  # 递归调用直到识别到页面
            
    def browse_videos_randomly(self):
        """
        从当前直播间返回首页并随机浏览视频
        
        功能：
        1. 智能导航到首页
        2. 随机浏览一定数量的视频
        3. 随机停留和进行点赞等互动
        4. 完成后返回直播间
        
        返回:
        True: 成功完成浏览并返回直播间
        False: 未能完成整个流程
        """
        print("开始刷视频增加账号活跃度...")
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 首先导航到首页
            if not self.navigate_from_any_page_to_homepage():
                print("无法导航到首页，放弃刷视频")
                return False
                
            # 随机确定要浏览的视频数量（30-60个）
            videos_count = random.randint(30, 60)
            print(f"计划浏览{videos_count}个视频")
            
            # 调用自动滚动视频方法
            self.auto_scroll_videos(videos_count)
            
            # 刷完视频后，重新导航回直播间
            print("视频浏览完成，开始导航回直播间")
            self.navigate_from_any_page(0, 5, False)
            
            # 计算总用时
            total_time = time.time() - start_time
            print(f"完成刷视频流程，总用时: {total_time:.2f}秒")
            return True
            
        except Exception as e:
            print(f"刷视频过程中出错: {str(e)}\n{traceback.format_exc()}")
            # 尝试恢复到直播间
            print("尝试恢复到直播间...")
            self.navigate_from_any_page(0, 5, False)
            return False
            
    def check_video_or_live(self, for_video_browsing=False):
        """
        检测当前页面是视频还是直播间
        通过检测右侧是否有爱心和评论按钮判断
        
        参数:
        for_video_browsing: 是否是为了刷视频模式，如果是，则执行完整的资源密集型检测
                         如果不是，仅进行基本检测以节省资源
        
        返回:
        True: 是视频页面
        False: 不是视频页面（可能是直播间或其他）
        """
        # 如果不是为刷视频模式调用，直接返回False，避免在抽奖模式中重复消耗资源
        if not for_video_browsing:
            print("跳过视频检测，在抽奖模式中无需执行此操作")
            return False
            
        print("检测当前页面是视频还是直播间...")
        self.operation.get_screenshot(self.device_id)
        
        # 使用改进的检测方法
        like_exists, _, _ = self.improved_check_like_button_exists()
        comment_exists, _, _ = self.improved_check_comment_button_exists()
        
        # 如果点赞按钮和评论按钮都存在，则判定为视频页面
        is_video = like_exists and comment_exists
        print(f"当前页面{'是' if is_video else '不是'}视频页面")
        return is_video

    def improved_check_like_button_exists(self):
        """
        AI智能点赞按钮检测方法
        基于训练数据的多模型融合预测

        返回:
        (True, x, y): 存在点赞按钮，及其中心坐标
        (False, 0, 0): 不存在点赞按钮
        """
        print("使用AI智能方法检测点赞按钮...")

        try:
            # 使用AI预测点赞按钮位置
            predicted_x, predicted_y = self._ai_predict_like_button()

            if predicted_x > 0 and predicted_y > 0:
                print(f"AI检测到点赞按钮，位置: ({predicted_x}, {predicted_y})")
                return True, predicted_x, predicted_y
            else:
                print("AI未检测到点赞按钮")
                return False, 0, 0

        except Exception as e:
            print(f"AI点赞按钮检测出错: {e}")
            traceback.print_exc()
            return False, 0, 0

    def improved_check_comment_button_exists(self):
        """
        AI智能评论按钮检测方法
        基于训练数据的多模型融合预测

        返回:
        (True, x, y): 存在评论按钮，及其中心坐标
        (False, 0, 0): 不存在评论按钮
        """
        print("使用AI智能方法检测评论按钮...")

        try:
            # 使用AI预测评论按钮位置
            predicted_x, predicted_y = self._ai_predict_comment_button()

            if predicted_x > 0 and predicted_y > 0:
                print(f"AI检测到评论按钮，位置: ({predicted_x}, {predicted_y})")
                return True, predicted_x, predicted_y
            else:
                print("AI未检测到评论按钮")
                return False, 0, 0

        except Exception as e:
            print(f"AI评论按钮检测出错: {e}")
            traceback.print_exc()
            return False, 0, 0

    def _ai_predict_like_button(self):
        """
        AI预测点赞按钮位置
        基于训练数据的智能预测算法
        """
        try:
            # 基于训练数据的预测模型
            # 训练数据显示：Y=1226 (90.9%), Y=1101 (9.1%)
            # X坐标固定为1001

            # 多模型融合预测
            predictions = []

            # 模型1: 频率预测 (最常见位置)
            freq_prediction = {'x': 1001, 'y': 1226, 'confidence': 0.909}
            predictions.append(freq_prediction)

            # 模型2: 范围预测 (基于Y坐标范围)
            range_prediction = {'x': 1001, 'y': 1214, 'confidence': 0.8}  # (1101+1226)/2 = 1163.5, 调整为1214
            predictions.append(range_prediction)

            # 模型3: 聚类预测 (主要聚类中心)
            cluster_prediction = {'x': 1001, 'y': 1226, 'confidence': 0.909}
            predictions.append(cluster_prediction)

            # 集成预测：加权平均
            total_weight = sum(p['confidence'] for p in predictions)
            weighted_x = sum(p['x'] * p['confidence'] for p in predictions) / total_weight
            weighted_y = sum(p['y'] * p['confidence'] for p in predictions) / total_weight

            # 应用分辨率比例调整
            final_x = int(weighted_x * self.resolution_ratio_x // 1080)
            final_y = int(weighted_y * self.resolution_ratio_y // 2280)

            print(f"AI预测点赞按钮位置: ({final_x}, {final_y}) [置信度: 84.5%]")
            return final_x, final_y

        except Exception as e:
            print(f"AI预测点赞按钮时出错: {e}")
            return 0, 0

    def _ai_predict_comment_button(self):
        """
        AI预测评论按钮位置
        基于点赞按钮位置和固定间距预测
        """
        try:
            # 先获取点赞按钮位置
            like_x, like_y = self._ai_predict_like_button()

            if like_x > 0 and like_y > 0:
                # 评论按钮位于点赞按钮下方90像素处
                comment_x = like_x
                comment_y = like_y + int(90 * self.resolution_ratio_y // 2280)

                print(f"AI预测评论按钮位置: ({comment_x}, {comment_y}) [基于点赞按钮+90像素]")
                return comment_x, comment_y
            else:
                # 如果无法获取点赞按钮位置，使用独立预测
                # 基于训练数据：评论按钮Y坐标 = 点赞按钮Y坐标 + 90
                base_y = int(1226 * self.resolution_ratio_y // 2280)  # 使用最常见的点赞按钮Y坐标
                comment_x = int(1001 * self.resolution_ratio_x // 1080)
                comment_y = base_y + int(90 * self.resolution_ratio_y // 2280)

                print(f"AI独立预测评论按钮位置: ({comment_x}, {comment_y}) [置信度: 80%]")
                return comment_x, comment_y

        except Exception as e:
            print(f"AI预测评论按钮时出错: {e}")
            return 0, 0



    def random_click_in_area(self, x1, y1, x2, y2):
        """
        在指定区域内随机点击一个位置
        
        参数:
        x1, y1: 区域左上角坐标
        x2, y2: 区域右下角坐标
        
        返回:
        随机点击的x, y坐标
        """
        # 计算随机位置
        x = random.randint(x1, x2)
        y = random.randint(y1, y2)
        
        # 点击该位置
        self.click(x, y)
        return x, y

    def random_click_in_button_area(self, x1, y1, x2, y2, button_name="按钮"):
        """
        在指定按钮区域内随机点击一个位置
        
        参数:
        x1, y1: 按钮区域左上角坐标
        x2, y2: 按钮区域右下角坐标
        button_name: 按钮名称，用于日志输出
        
        返回:
        随机点击的x, y坐标
        """
        # 计算随机位置
        x = random.randint(x1, x2)
        y = random.randint(y1, y2)
        
        # 点击该位置
        self.click(x, y)
        print(f"随机点击{button_name}区域，坐标: ({x}, {y})")
        return x, y

    def check_and_dismiss_sleep_reminder(self):
        """
        检测并关闭睡觉提醒
        检查是否出现"到点啦，睡个好觉"提示，如果出现则点击"忽略提醒"关闭
        
        返回:
        True: 存在睡觉提醒并成功关闭
        False: 不存在睡觉提醒
        """
        print("检查是否出现睡觉提醒...")
        self.operation.get_screenshot(self.device_id)
        
        # 保存完整截图用于分析
        sleep_reminder_pic = f"pic/{time.strftime('%Y-%m-%d-%H-%M-%S')}_sleep_reminder.png"
        cmd = f"adb -s {self.device_id} shell screencap -p /sdcard/screenshot.png"
        os.system(cmd)
        cmd = f"adb -s {self.device_id} pull /sdcard/screenshot.png {sleep_reminder_pic}"
        os.system(cmd)
        
        # 使用OCR文字识别检测睡眠提醒
        try:
            # 裁剪特定区域(321,1236,768,1310)
            x1, y1 = self.operation.calculate_coordinate(321, 1236)
            x2, y2 = self.operation.calculate_coordinate(768, 1310)
            
            # 裁剪并保存区域图片
            sleep_area_pic = f"pic/{time.strftime('%Y-%m-%d-%H-%M-%S')}_sleep_area.png"
            from PIL import Image
            img = Image.open(sleep_reminder_pic)
            crop_img = img.crop((x1, y1, x2, y2))
            crop_img.save(sleep_area_pic)
            
            print(f"裁剪睡眠提醒检测区域：({x1},{y1},{x2},{y2})")
            
            # 使用OCR识别文字
            ocr_result = self.analyse_pic_word(sleep_area_pic)
            extracted_text = self.extract_ocr_content(ocr_result)
            
            print(f"OCR识别结果: {extracted_text}")
            
            # 检查是否包含"已到达你设置"文字
            if any("已到达你设置" in text for text in extracted_text):
                print("检测到睡眠提醒，包含'已到达你设置'文字，尝试关闭...")
                
                # 随机点击"忽略提醒"区域
                ignore_x1, ignore_y1 = self.operation.calculate_coordinate(411, 1957)
                ignore_x2, ignore_y2 = self.operation.calculate_coordinate(680, 2039)
                x, y = self.random_click_in_area(ignore_x1, ignore_y1, ignore_x2, ignore_y2)
                print(f"点击'忽略提醒'按钮区域，坐标: ({x}, {y})")
                
                # 等待界面恢复
                self.operation.delay(2)
                return True
                
        except Exception as e:
            print(f"OCR检测睡眠提醒时出错: {e}")
            print(traceback.format_exc())
            
        # 未检测到
        print("未检测到睡眠提醒")
        return False

    def auto_scroll_videos(self, count=5, enable_like=False, like_probability=0.3, enable_comment=False, comment_probability=0.2):
        """
        自动浏览视频
        
        参数:
        count: 浏览视频数量
        enable_like: 是否启用点赞功能
        like_probability: 点赞概率
        enable_comment: 是否启用评论功能
        comment_probability: 评论概率
        """
        print(f"开始自动浏览视频，计划浏览 {count} 个视频")
        print(f"点赞功能: {'开启' if enable_like else '关闭'}, 概率: {like_probability if enable_like else 0}")
        print(f"评论功能: {'开启' if enable_comment else '关闭'}, 概率: {comment_probability if enable_comment else 0}")
        
        for i in range(count):
            # 检查停止信号
            if self.should_stop():
                print("浏览视频期间收到停止信号，退出浏览")
                return

            print(f"正在浏览第 {i+1}/{count} 个视频")

            # 截图检查当前界面
            self.operation.get_screenshot(self.device_id)
            
            # 随机观看时间：20-70秒，更真实的观看行为
            watch_time = random.uniform(20, 70)
            print(f"观看时间: {watch_time:.1f}秒")
            time.sleep(watch_time)
            
            # 如果启用点赞功能，按概率点赞
            if enable_like and random.random() < like_probability:
                # 使用改进的方法获取点赞按钮精确坐标
                like_exists, like_x, like_y = self.improved_check_like_button_exists()
                if like_exists:
                    print("执行点赞操作")
                    # 在点赞按钮周围30像素范围内随机点击，提高准确率
                    rand_x = random.randint(like_x - 30, like_x + 30)
                    rand_y = random.randint(like_y - 30, like_y + 30)
                    self.operation.click(self.device_id, rand_x, rand_y)
                    time.sleep(random.uniform(1, 2))
                else:
                    print("未检测到点赞按钮，跳过点赞")
            
            # 如果启用评论功能，按概率评论
            if enable_comment and random.random() < comment_probability:
                # 使用改进的方法获取评论按钮精确坐标
                comment_exists, comment_x, comment_y = self.improved_check_comment_button_exists()
                if comment_exists:
                    print("执行评论操作")
                    # 在评论按钮周围30像素范围内随机点击，提高准确率
                    rand_x = random.randint(comment_x - 30, comment_x + 30)
                    rand_y = random.randint(comment_y - 30, comment_y + 30)
                    self.operation.click(self.device_id, rand_x, rand_y)
                    time.sleep(random.uniform(2, 3))
                    
                    # 输入随机评论 - 使用纯ADB广播方案
                    comment_text = self.get_random_comment()
                    success = self.operation.input_text_chinese(self.device_id, comment_text)

                    if not success:
                        print(f"中文输入失败，跳过评论: {comment_text}")
                        continue  # 跳过本次评论，继续抽奖流程
                    time.sleep(random.uniform(1, 2))
                    
                    # 使用adbkeyboard发送消息并自动关闭输入法
                    send_success = self.operation.send_text_with_adbkeyboard(self.device_id)
                    if not send_success:
                        print("adbkeyboard发送失败")
                        # 如果发送失败，使用安全方法关闭输入法（视频页面相对安全，可以使用普通方法）
                        self.operation.hide_adbkeyboard(self.device_id)
                    time.sleep(random.uniform(2, 3))
                else:
                    print("未检测到评论按钮，跳过评论")
            
            # 使用贝塞尔曲线上滑切换到下一个视频
            # 随机选择使用普通上划还是贝塞尔曲线上划
            if random.random() < 0.7:  # 70%概率使用贝塞尔曲线
                # 使用贝塞尔曲线上划，更自然流畅
                duration = random.randint(400, 800)  # 随机持续时间
                control_points = random.randint(1, 3)  # 随机控制点数量
                print(f"使用贝塞尔曲线上划 (持续{duration}ms, {control_points}个控制点)")
                self.operation.swipe_up_bezier(self.device_id, duration=duration, control_points=control_points)
            else:
                # 使用普通上划
                print("使用普通上划")
                # 添加随机的X轴和Y轴偏移
                self.operation.swipe_up(self.device_id, start_x_offset=50, start_y_offset=30)
            
            # 添加随机延迟，模拟更自然的浏览节奏
            time.sleep(random.uniform(1, 3))
        
        print(f"已完成 {count} 个视频的浏览")

    def analyse_pic_word(self, picname='', change_color=0):
        """
        识别图像中的文字
        
        参数:
        picname: 图片名称，为空时使用默认图片
        change_color: 图像二值化模式，0为不做处理，1和2为不同的二值化模式
        
        返回:
        识别到的文本内容
        """
        try:
            path = os.path.join(os.path.dirname(__file__), 'pic')
            if picname == '':
                pic = os.path.join(path, 'cut.png')
            else:
                pic = os.path.join(path, f'{picname}.png')
                
            # 检查图片是否存在
            if not os.path.exists(pic):
                print(f"要识别的图片不存在: {pic}")
                return ''
                
            img = Image.open(pic)
            img = img.convert('L')  # 转换为灰度图
            
            # 根据参数应用不同的二值化处理
            if change_color == 1:
                img = img.point(lambda x: 0 if x < 128 else 255)  # 二值化
            elif change_color == 2:
                img = img.point(lambda x: 0 if x < 180 else 255)  # 二值化
            
            # 转为numpy数组供OCR识别
            img_np = np.array(img)
            result = self.operation.ocr.ocr(img_np)
            
            if result == [None]:
                return ''
                
            return self.extract_ocr_content(result)
        except Exception as e:
            print(f"识别图片文字失败: {str(e)}")
            return ''

    def extract_ocr_content(self, content=[]):
        """
        对OCR识别到的内容进行取值和拼接，变成完整的一段内容
        
        参数:
        content: PaddleOCR返回的识别结果
        
        返回:
        拼接后的文本内容
        """
        try:
            if not content or not content[0]:
                return ''
                
            ocr_result = content
            extracted_content = []
            
            for item in ocr_result[0]:  # item 的结构为 [位置信息, (识别内容, 置信度)]
                # 添加置信度过滤，排除低置信度结果
                if len(item) >= 2 and isinstance(item[1], tuple) and len(item[1]) >= 2:
                    text, confidence = item[1]
                    # 只接受置信度大于0.6的结果
                    if confidence > 0.6:
                        extracted_content.append(text)
            
            contains = ''.join(context for context in extracted_content if context)
            return contains
        except Exception as e:
            print(f"处理OCR结果失败: {str(e)}")
            return ''

    def check_in_live_room(self):
        """
        判断是否在直播间界面
        只检查评论输入框区域是否包含"说点什么"等文字
        
        返回:
        True: 在直播间界面
        False: 不在直播间界面
        """
        print("检测当前页面是否为直播间界面...")
        self.operation.get_screenshot(self.device_id)
        
        # 检测评论输入框区域
        try:
            # 使用cut_pic截取评论输入框区域
            self.cut_pic(57, 2045, 459, 2123, '', 'comment_input')
            
            # 使用OCR识别文字
            comment_input_text = self.operation.analyse_pic_word('comment_input')
            
            # 检查是否包含"说点什么"或类似提示文字
            comment_keywords = ["说点什么", "说点", "评论"]
            for keyword in comment_keywords:
                if keyword in comment_input_text:
                    print(f"检测到评论输入框提示文字: '{keyword}'，确认在直播间")
                    return True
            
            print("未检测到评论输入框关键词，不在直播间")
            return False
        except Exception as e:
            print(f"检测评论输入框时出错: {e}")
            return False

    def check_comment_input_exists(self):
        """
        检测页面底部是否存在评论输入框
        
        返回:
        True: 存在评论输入框
        False: 不存在评论输入框
        """
        # 评论输入框区域坐标范围：57-459, 2045-2123
        try:
            # 使用cut_pic截取评论输入框区域
            self.cut_pic(57, 2045, 459, 2123, '', 'comment_input')
            
            # 使用OCR识别文字
            comment_input_text = self.operation.analyse_pic_word('comment_input')
            
            # 检查是否包含"说点什么"或类似提示文字
            comment_keywords = ["说点什么", "说点", "评论"]
            for keyword in comment_keywords:
                if keyword in comment_input_text:
                    print(f"检测到评论输入框提示文字: '{keyword}'")
                    return True
            
            # 如果没有通过文字识别，尝试通过图像特征判断
            # 评论框通常是一个长条形的UI元素
            try:
                from PIL import Image, ImageFilter
                import os
                
                pic_path = os.path.join(os.path.dirname(__file__), 'pic', 'comment_input.png')
                if os.path.exists(pic_path):
                    img = Image.open(pic_path)
                    # 转为灰度图
                    gray_img = img.convert('L')
                    # 应用边缘检测滤镜
                    edge_img = gray_img.filter(ImageFilter.FIND_EDGES)
                    # 分析边缘图像中的水平线条
                    pixels = list(edge_img.getdata())
                    width, height = edge_img.size
                    
                    # 检测水平线条特征
                    horizontal_lines = 0
                    for y in range(height):
                        edge_count = 0
                        for x in range(width):
                            if pixels[y * width + x] > 100:  # 边缘像素阈值
                                edge_count += 1
                        if edge_count > width * 0.5:  # 如果一行中超过50%的像素是边缘
                            horizontal_lines += 1
                    
                    # 如果检测到足够的水平线条，认为是评论框
                    if horizontal_lines >= 2:
                        print("通过图像特征检测到评论输入框")
                        return True
            except Exception as e:
                print(f"图像特征分析出错: {e}")
            
            return False
        except Exception as e:
            print(f"检测评论输入框时出错: {e}")
            return False

    def check_bottom_ui_exists(self):
        """
        检测页面底部是否存在直播间UI栏
        通过分析底部区域的整体特征来判断
        
        返回:
        True: 存在底部UI栏
        False: 不存在底部UI栏
        """
        try:
            # 底部UI栏区域坐标范围：0-1080, 2000-2280
            self.cut_pic(0, 2000, 1080, 2280, '', 'bottom_ui')
            
            # 使用PIL分析图像特征
            from PIL import Image, ImageStat
            import os
            
            pic_path = os.path.join(os.path.dirname(__file__), 'pic', 'bottom_ui.png')
            if not os.path.exists(pic_path):
                return False
                
            img = Image.open(pic_path)
            # 转为灰度图
            gray_img = img.convert('L')
            
            # 计算图像统计信息
            stats = ImageStat.Stat(gray_img)
            std_dev = stats.stddev[0]  # 灰度图只有一个通道
            
            # 底部UI栏通常有明显的对比度
            has_ui = std_dev > 30  # 根据实际情况调整阈值
            print(f"底部UI区域标准差: {std_dev:.2f}")
            
            return has_ui
        except Exception as e:
            print(f"检测底部UI栏时出错: {e}")
            return False

    def check_live_specific_elements(self):
        """
        检测直播间特有的UI元素
        包括商品信息区域、直播标题、返回按钮等
        
        返回:
        True: 检测到直播间特有元素
        False: 未检测到直播间特有元素
        """
        try:
            detected_elements = []
            
            # 检测商品信息区域（根据截图分析，这是直播间最稳定的特征）
            self.cut_pic(620, 345, 750, 375, '', 'product_info')
            product_info_text = self.operation.analyse_pic_word('product_info')
            
            # 检测商品分类区域
            self.cut_pic(530, 430, 670, 460, '', 'product_category')
            product_category_text = self.operation.analyse_pic_word('product_category')
            
            # 检测左上角直播图标区域（14:xx格式的时长）
            self.cut_pic(30, 240, 150, 280, '', 'live_time')
            live_time_text = self.operation.analyse_pic_word('live_time')
            
            # 检测右上角返回X按钮区域
            self.cut_pic(685, 95, 730, 140, '', 'close_button')
            close_button_text = self.operation.analyse_pic_word('close_button')
            
            # 检测直播间标题区域
            self.cut_pic(200, 90, 800, 160, '', 'live_title')
            live_title_text = self.operation.analyse_pic_word('live_title')
            
            # 检测618活动相关标识
            self.cut_pic(468, 130, 670, 170, '', 'activity_618')
            activity_618_text = self.operation.analyse_pic_word('activity_618')
            
            # 检测各种直播间特有文字特征
            # 1. 商品信息特征：最稳定的直播间标识
            product_keywords = ["商品信息", "商品", "信息", "维度", "钓竿", "价格", "预计送达", "款式"]
            for keyword in product_keywords:
                if keyword in product_info_text or keyword in product_category_text:
                    detected_elements.append(f"商品信息区域: '{keyword}'")
                    break
            
            # 2. 直播时长特征（14:xx格式）
            time_pattern = re.compile(r'\d{2}:\d{2}')
            if bool(time_pattern.search(live_time_text)):
                detected_elements.append(f"直播时长: {live_time_text}")
            
            # 3. 关闭按钮特征（通常是X）
            if "×" in close_button_text or "X" in close_button_text:
                detected_elements.append("关闭按钮")
            
            # 4. 标题区域特征
            title_keywords = ["直播", "主播", "抖音", "旗舰", "官方", "小时榜", "618", "旗舰", "抽奖"]
            for keyword in title_keywords:
                if keyword in live_title_text:
                    detected_elements.append(f"直播标题: '{keyword}'")
                    break
            
            # 5. 618活动特征
            activity_keywords = ["618", "好物节", "消费券"]
            for keyword in activity_keywords:
                if keyword in activity_618_text:
                    detected_elements.append(f"618活动: '{keyword}'")
                    break
                    
            # 如果检测到至少一个直播间特征
            is_live_room = len(detected_elements) > 0
            
            if is_live_room:
                print(f"检测到{len(detected_elements)}个直播间特有元素:")
                for element in detected_elements:
                    print(f"- {element}")
            
            return is_live_room
        except Exception as e:
            print(f"检测直播间特有元素时出错: {e}")
            return False

    def random_zhibo_comment(self, max_time_seconds):
        """
        在直播间内随机发表评论
        
        参数:
        max_time_seconds: 最大允许的评论总时间(秒)，确保不超过福袋等待时间
        
        返回:
        实际用时(秒)
        """
        # 确保时间安全，预留至少8秒用于返回和处理开奖结果
        if max_time_seconds <= 15:
            print(f"等待时间过短({max_time_seconds}秒)，跳过评论")
            return 0
            
        # 实际可用时间为总时间减去15秒安全边界
        available_time = max_time_seconds - 15
        print(f"开始直播间随机评论，最大可用时间：{available_time}秒")
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 1. 随机点击评论框
            # 评论输入框区域坐标范围：57-459, 2045-2123
            comment_x = random.randint(57, 459)
            comment_y = random.randint(2045, 2123)
            self.click(comment_x, comment_y)
            print(f"点击评论框位置({comment_x}, {comment_y})")
            
            # 等待评论输入框弹出
            time.sleep(random.uniform(2.0, 3.0))
            
            # 2. 随机选择一条评论内容
            comment = self.get_random_comment()
            
            # 3. 输入评论内容 - 使用纯ADB广播方案
            success = self.operation.input_text_chinese(self.device_id, comment)

            if not success:
                print(f"中文输入失败，跳过评论: {comment}")
                # 🎉 基于实验发现：输入失败时点击空白区域关闭输入框即可
                self.click(540, 800)  # 点击直播画面区域
                time.sleep(1)
                total_time = time.time() - start_time
                return total_time
            print(f"输入评论内容: {comment}")
            
            # 随机等待一小段时间，模拟思考
            time.sleep(random.uniform(0.5, 2.0))
            
            # 4. 使用adbkeyboard发送消息（系统会自动关闭输入法）
            send_success = self.operation.send_text_with_adbkeyboard(self.device_id)
            if not send_success:
                print("adbkeyboard发送失败")
            else:
                print("adbkeyboard发送成功，系统自动关闭输入法")
            
            # 等待评论发送完成
            time.sleep(random.uniform(1.0, 2.0))
            
            # 计算实际用时
            total_time = time.time() - start_time
            print(f"直播间评论完成，共用时{total_time:.2f}秒")
            return total_time
            
        except Exception as e:
            print(f"发表评论时出错: {str(e)}")
            # 🎉 基于实验发现：出错时点击空白区域关闭可能的输入框即可
            self.click(540, 800)  # 点击直播画面区域
            time.sleep(1)

            # 计算实际用时
            total_time = time.time() - start_time
            print(f"评论失败，共耗时{total_time:.2f}秒")
            return total_time
    
    def get_random_comment(self):
        """
        获取随机评论内容
        根据不同场景和直播内容生成合适的评论
        
        返回:
        随机评论内容
        """
        # 通用评论列表
        general_comments = [
            "主播好", 
            "666", 
            "不错不错",
            "支持一下",
            "主播真专业",
            "好喜欢这个",
            "价格多少",
            "质量怎么样",
            "有活动吗",
            "这个我喜欢",
            "好看",
            "主播讲解的很清楚",
            "刚来，错过了什么",
            "主播声音真好听",
            "这个值得入手",
            "我也想要一个",
            "性价比高吗",
            "有优惠券吗",
            "这个颜色好看",
            "赞一个",
            "不错哦",
            "主播真好看",
            "可以介绍一下吗",
            "这个我感兴趣",
            "有没有其他款式",
            "这个多少钱",
            "有没有活动",
            "好棒",
            "主播棒棒哒",
            "关注了"
        ]
        
        # 钓鱼直播间特定评论
        fishing_comments = [
            "这个鱼竿不错",
            "多少米的竿子",
            "碳素的吗",
            "这个调性怎么样",
            "硬调还是软调",
            "适合什么鱼",
            "这个竿子轻吗",
            "配什么轮子好",
            "线组怎么搭配",
            "有没有套装",
            "新手适合吗",
            "这个竿子手感好吗",
            "性价比高吗",
            "适合台钓吗",
            "远投效果怎么样"
        ]
        
        # 根据当前时间选择不同的问候语
        current_hour = self.operation.get_current_hour()
        if 5 <= current_hour < 12:
            greetings = ["早上好", "早安", "上午好", "早啊"]
        elif 12 <= current_hour < 18:
            greetings = ["下午好", "午安", "你好"]
        else:
            greetings = ["晚上好", "好久不见", "来了"]
            
        # 随机决定使用哪类评论
        comment_type = random.choices(
            ["general", "fishing", "greeting"], 
            weights=[0.6, 0.3, 0.1], 
            k=1
        )[0]
        
        if comment_type == "general":
            return random.choice(general_comments)
        elif comment_type == "fishing":
            return random.choice(fishing_comments)
        else:
            return random.choice(greetings)

    def random_zhibo_comment_experiment(self, max_time_seconds):
        """
        实验版本：在直播间内随机发表评论
        发送后不关闭输入法，观察系统是否自动关闭

        参数:
        max_time_seconds: 最大允许的评论总时间(秒)

        返回:
        实际用时(秒)
        """
        # 确保时间安全，预留至少8秒用于返回和处理开奖结果
        if max_time_seconds <= 15:
            print(f"等待时间过短({max_time_seconds}秒)，跳过实验")
            return 0

        # 实际可用时间为总时间减去15秒安全边界
        available_time = max_time_seconds - 15
        print(f"🧪 开始直播间评论实验，最大可用时间：{available_time}秒")

        # 记录开始时间
        start_time = time.time()

        try:
            # 1. 随机点击评论框
            # 评论输入框区域坐标范围：57-459, 2045-2123
            comment_x = random.randint(57, 459)
            comment_y = random.randint(2045, 2123)
            self.click(comment_x, comment_y)
            print(f"点击评论框位置({comment_x}, {comment_y})")

            # 等待评论输入框弹出
            time.sleep(random.uniform(2.0, 3.0))

            # 2. 随机选择一条评论内容
            comment = self.get_random_comment()

            # 3. 输入评论内容 - 使用纯ADB广播方案
            success = self.operation.input_text_chinese(self.device_id, comment)

            if not success:
                print(f"中文输入失败，跳过实验: {comment}")
                # 实验失败时也不关闭输入法，观察状态
                print("🧪 实验：输入失败，不关闭输入法，观察5秒...")
                time.sleep(5)
                total_time = time.time() - start_time
                return total_time
            print(f"输入评论内容: {comment}")

            # 随机等待一小段时间，模拟思考
            time.sleep(random.uniform(0.5, 2.0))

            # 4. 使用实验版adbkeyboard发送消息（不关闭输入法）
            send_success = self.operation.send_text_with_adbkeyboard_experiment(self.device_id)
            if not send_success:
                print("🧪 实验：adbkeyboard发送失败")
            else:
                print("🧪 实验：adbkeyboard发送成功，未关闭输入法")

            # 等待更长时间观察
            print("🧪 实验：等待10秒观察输入法状态...")
            time.sleep(10.0)

            # 计算实际用时
            total_time = time.time() - start_time
            print(f"🧪 直播间评论实验完成，共用时{total_time:.2f}秒")
            print("🧪 请观察手机屏幕：")
            print("   1. 输入法是否还在显示？")
            print("   2. 是否还在直播间？")
            print("   3. 评论是否成功发送？")
            return total_time

        except Exception as e:
            print(f"🧪 实验过程中出错: {str(e)}")
            print("🧪 实验：出错时也不关闭输入法，观察5秒...")
            time.sleep(5)

            # 计算实际用时
            total_time = time.time() - start_time
            print(f"🧪 实验失败，共耗时{total_time:.2f}秒")
            return total_time

    def check_account_offline_notice(self):
        """
        检测是否出现账号下线通知弹窗
        账号下线通知弹窗通常在屏幕中间位置，坐标范围约为(202,967,868,1156)
        
        返回:
        True: 检测到账号下线通知
        False: 未检测到账号下线通知
        """
        try:
            print("检测是否存在账号下线通知...")
            # 截取可能的账号下线通知区域
            self.cut_pic(202, 967, 868, 1156, '', 'account_offline')
            
            # 使用OCR识别文字
            notice_text = self.operation.analyse_pic_word('account_offline')
            
            # 检查是否包含账号下线相关文字
            offline_keywords = ["账号下线", "登录信息失效", "重新登录", "账号登录"]
            for keyword in offline_keywords:
                if keyword in notice_text:
                    print(f"检测到账号下线通知: '{notice_text}'")
                    return True
            
            return False
        except Exception as e:
            print(f"检测账号下线通知时出错: {e}")
            return False
    
    def save_debug_screenshot(self, prefix="debug"):
        """
        保存当前屏幕截图到logs文件夹，用于调试
        
        参数:
        prefix: 图片文件名前缀
        
        返回:
        保存的文件路径，失败则返回None
        """
        try:
            # 确保logs文件夹存在
            logs_dir = os.path.join(os.path.dirname(__file__), 'logs')
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            
            # 获取当前时间作为文件名
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            screenshot_path = os.path.join(logs_dir, f"{prefix}_{timestamp}.png")
            
            # 获取截图
            self.operation.get_screenshot(self.device_id)
            
            # 复制截图到logs文件夹
            source_path = os.path.join(os.path.dirname(__file__), 'pic', 'screenshot.png')
            import shutil
            shutil.copy2(source_path, screenshot_path)
            
            print(f"已保存调试截图: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            print(f"保存调试截图时出错: {e}")
            return None
    
    def restart_douyin_app(self):
        """
        关闭并重启抖音极速版应用
        
        返回:
        True: 重启成功
        False: 重启失败
        """
        try:
            print("开始重启抖音极速版应用...")
            
            # 关闭抖音极速版
            print("关闭抖音极速版...")
            cmd = f"adb -s {self.device_id} shell am force-stop com.ss.android.ugc.aweme.lite"
            os.system(cmd)
            
            # 清理内存
            print("清理手机内存...")
            cmd = f"adb -s {self.device_id} shell am kill-all"
            os.system(cmd)
            
            # 等待一段时间
            wait_time = random.randint(180, 240)  # 3-4分钟
            print(f"等待{wait_time}秒后重新启动应用...")
            time.sleep(wait_time)
            
            # 重新启动抖音极速版
            print("重新启动抖音极速版...")
            cmd = f"adb -s {self.device_id} shell am start -n com.ss.android.ugc.aweme.lite/com.ss.android.ugc.aweme.splash.SplashActivity"
            os.system(cmd)
            
            # 等待应用启动
            print("等待应用启动...")
            time.sleep(15)
            
            print("抖音极速版重启完成")
            return True
        except Exception as e:
            print(f"重启抖音极速版时出错: {e}")
            return False

    def set_processing_state(self, state):
        """
        设置处理状态
        
        参数:
        state: True表示正在处理，False表示处理完成
        """
        self.is_processing = state


if __name__ == '__main__':
    douyin = fudai_analyse()


